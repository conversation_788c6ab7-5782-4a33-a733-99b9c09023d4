<?php

declare(strict_types=1);

namespace App\Modules\Employee\Services;

use App\Core\Validation\Validator;
use App\Modules\Employee\Models\Employee;
use App\Modules\Employee\Repositories\EmployeeRepository;
use System\Exception\SystemException;

class EmployeeService {
   private array $validationRules = [
      'store_id' => ['required', 'numeric'],
      'name' => ['required', 'max:100'],
      'email' => ['required', 'email', 'max:100'],
      'phone' => ['required', 'max:20'],
      'position' => ['required', 'max:50']
   ];

   public function __construct(
      private EmployeeRepository $employeeRepository,
      private Validator $validator
   ) {
   }

   /**
    * Tüm çalışanları getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $employees = $this->employeeRepository->getAll();
      return array_map(function ($employee) {
         return $employee->jsonSerialize();
      }, $employees);
   }

   /**
    * Belirli bir ID'ye sahip çalışanı getirir.
    *
    * @param int $id
    * @return Employee|null
    */
   public function getById(int $id): ?Employee {
      $employee = $this->employeeRepository->getById($id);
      if (!$employee) {
         return null;
      }
      return $employee;
   }

   /**
    * Yeni bir çalışan oluşturur.
    *
    * @param array $data Çalışan verileri
    * @return Employee|null
    */
   public function create(array $data): ?Employee {
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      return $this->employeeRepository->create($data);
   }

   /**
    * Bir çalışanın bilgilerini günceller.
    *
    * @param int $id Çalışan ID'si
    * @param array $data Güncellenecek veriler
    * @return Employee|null Güncelleme başarılıysa Employee nesnesi, değilse null döner.
    */
   public function update(int $id, array $data): ?Employee {
      $employee = $this->employeeRepository->getById($id);

      if (!$employee) {
         throw new SystemException('Employee not found');
      }

      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      return $this->employeeRepository->update($id, $data);
   }

   /**
    * Bir çalışanı siler.
    *
    * @param int $id Çalışan ID'si
    * @return bool Silme başarılıysa true, değilse false
    */
   public function delete(int $id): bool {
      $employee = $this->employeeRepository->getById($id);

      if (!$employee) {
         throw new SystemException('Employee not found');
      }

      return $this->employeeRepository->delete($id);
   }

   /**
    * Belirli bir mağazaya ait çalışanları getirir.
    *
    * @param int $storeId Mağaza ID'si
    * @return array
    */
   public function getByStoreId(int $storeId): array {
      return $this->employeeRepository->getByStoreId($storeId);
   }
}
