<?php

declare(strict_types=1);

namespace App\Modules\Analytics\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Analytics\Models\AnalyticsMetric;
use System\Database\Database;
use System\Exception\SystemException;

class AnalyticsMetricRepository extends BaseRepository {
   protected string $table = 'analytics_metrics';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu AnalyticsMetric nesnesine dönüştürür.
    *
    * @param array $data
    * @return AnalyticsMetric
    */
   protected function mapToObject(array $data): AnalyticsMetric {
      return new AnalyticsMetric(...$data);
   }

   /**
    * Yeni bir metrik kaydeder veya mevcut metriği günceller.
    *
    * @param array $data
    * @return AnalyticsMetric
    */
   public function createOrUpdate(array $data): AnalyticsMetric {
      try {
         // Önce aynı metrik, boyut ve periyot için kayıt var mı kontrol et
         $existingMetric = $this->findMetric(
            $data['metric_name'],
            $data['period_type'],
            $data['period_start'],
            $data['period_end'],
            $data['dimension'] ?? null,
            $data['dimension_value'] ?? null
         );

         if ($existingMetric) {
            // Mevcut metriği güncelle
            $stmt = $this->database->prepare(
               "UPDATE {$this->table}
               SET metric_value = :metric_value, updated_at = NOW()
               WHERE id = :id"
            );

            $stmt->execute([
               'metric_value' => $data['metric_value'],
               'id' => $existingMetric->getId()
            ]);

            return $this->getById($existingMetric->getId());
         } else {
            // Yeni metrik oluştur
            $stmt = $this->database->prepare(
               "INSERT INTO {$this->table}
               (metric_name, metric_value, dimension, dimension_value, period_type, period_start, period_end)
               VALUES (:metric_name, :metric_value, :dimension, :dimension_value, :period_type, :period_start, :period_end)"
            );

            $params = [
               'metric_name' => $data['metric_name'],
               'metric_value' => $data['metric_value'],
               'dimension' => $data['dimension'] ?? null,
               'dimension_value' => $data['dimension_value'] ?? null,
               'period_type' => $data['period_type'],
               'period_start' => $data['period_start'],
               'period_end' => $data['period_end']
            ];

            $stmt->execute($params);
            $id = (int) $this->database->getLastId();

            return $this->getById($id);
         }
      } catch (\Exception $e) {
         throw new SystemException('Failed to create or update metric: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir metrik, boyut ve periyot için metrik arar.
    *
    * @param string $metricName
    * @param string $periodType
    * @param string $periodStart
    * @param string $periodEnd
    * @param string|null $dimension
    * @param string|null $dimensionValue
    * @return AnalyticsMetric|null
    */
   public function findMetric(
      string $metricName,
      string $periodType,
      string $periodStart,
      string $periodEnd,
      ?string $dimension = null,
      ?string $dimensionValue = null
   ): ?AnalyticsMetric {
      $sql = "SELECT * FROM {$this->table}
              WHERE metric_name = :metric_name
              AND period_type = :period_type
              AND period_start = :period_start
              AND period_end = :period_end";

      $params = [
         'metric_name' => $metricName,
         'period_type' => $periodType,
         'period_start' => $periodStart,
         'period_end' => $periodEnd
      ];

      if ($dimension !== null) {
         $sql .= " AND dimension = :dimension";
         $params['dimension'] = $dimension;
      } else {
         $sql .= " AND dimension IS NULL";
      }

      if ($dimensionValue !== null) {
         $sql .= " AND dimension_value = :dimension_value";
         $params['dimension_value'] = $dimensionValue;
      } else {
         $sql .= " AND dimension_value IS NULL";
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $result = $stmt->getRow();

      if (!$result) {
         return null;
      }

      return $this->mapToObject((array) $result);
   }

   /**
    * Belirli bir metrik adına göre metrikleri getirir.
    *
    * @param string $metricName
    * @param array $filters
    * @return array
    */
   public function getByMetricName(string $metricName, array $filters = []): array {
      $sql = "SELECT * FROM {$this->table} WHERE metric_name = :metric_name";
      $params = ['metric_name' => $metricName];

      if (!empty($filters['period_type'])) {
         $sql .= " AND period_type = :period_type";
         $params['period_type'] = $filters['period_type'];
      }

      if (!empty($filters['dimension'])) {
         $sql .= " AND dimension = :dimension";
         $params['dimension'] = $filters['dimension'];
      }

      if (!empty($filters['dimension_value'])) {
         $sql .= " AND dimension_value = :dimension_value";
         $params['dimension_value'] = $filters['dimension_value'];
      }

      if (!empty($filters['start_date'])) {
         $sql .= " AND period_start >= :start_date";
         $params['start_date'] = $filters['start_date'];
      }

      if (!empty($filters['end_date'])) {
         $sql .= " AND period_end <= :end_date";
         $params['end_date'] = $filters['end_date'];
      }

      $sql .= " ORDER BY period_start DESC";

      if (!empty($filters['limit'])) {
         $sql .= " LIMIT :limit";
         $params['limit'] = (int) $filters['limit'];
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $metrics = $stmt->getAll();

      return array_map(function ($metric) {
         return $this->mapToObject((array) $metric);
      }, $metrics);
   }

   /**
    * Belirli bir boyut ve değere göre metrikleri getirir.
    *
    * @param string $dimension
    * @param string $dimensionValue
    * @param array $filters
    * @return array
    */
   public function getByDimension(string $dimension, string $dimensionValue, array $filters = []): array {
      $sql = "SELECT * FROM {$this->table} WHERE dimension = :dimension AND dimension_value = :dimension_value";
      $params = [
         'dimension' => $dimension,
         'dimension_value' => $dimensionValue
      ];

      if (!empty($filters['metric_name'])) {
         $sql .= " AND metric_name = :metric_name";
         $params['metric_name'] = $filters['metric_name'];
      }

      if (!empty($filters['period_type'])) {
         $sql .= " AND period_type = :period_type";
         $params['period_type'] = $filters['period_type'];
      }

      if (!empty($filters['start_date'])) {
         $sql .= " AND period_start >= :start_date";
         $params['start_date'] = $filters['start_date'];
      }

      if (!empty($filters['end_date'])) {
         $sql .= " AND period_end <= :end_date";
         $params['end_date'] = $filters['end_date'];
      }

      $sql .= " ORDER BY period_start DESC, metric_name ASC";

      if (!empty($filters['limit'])) {
         $sql .= " LIMIT :limit";
         $params['limit'] = (int) $filters['limit'];
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $metrics = $stmt->getAll();

      return array_map(function ($metric) {
         return $this->mapToObject((array) $metric);
      }, $metrics);
   }

   /**
    * Belirli bir zaman aralığındaki metrikleri getirir.
    *
    * @param string $periodType
    * @param string $startDate
    * @param string $endDate
    * @param array $filters
    * @return array
    */
   public function getByTimeRange(string $periodType, string $startDate, string $endDate, array $filters = []): array {
      $sql = "SELECT * FROM {$this->table}
              WHERE period_type = :period_type
              AND period_start >= :start_date
              AND period_end <= :end_date";

      $params = [
         'period_type' => $periodType,
         'start_date' => $startDate,
         'end_date' => $endDate
      ];

      if (!empty($filters['metric_name'])) {
         $sql .= " AND metric_name = :metric_name";
         $params['metric_name'] = $filters['metric_name'];
      }

      if (!empty($filters['dimension'])) {
         $sql .= " AND dimension = :dimension";
         $params['dimension'] = $filters['dimension'];
      }

      if (!empty($filters['dimension_value'])) {
         $sql .= " AND dimension_value = :dimension_value";
         $params['dimension_value'] = $filters['dimension_value'];
      }

      $sql .= " ORDER BY period_start ASC, metric_name ASC";

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $metrics = $stmt->getAll();

      return array_map(function ($metric) {
         return $this->mapToObject((array) $metric);
      }, $metrics);
   }

   /**
    * Metrik trendini getirir (zaman içindeki değişim).
    *
    * @param string $metricName
    * @param string $periodType
    * @param string $startDate
    * @param string $endDate
    * @param string|null $dimension
    * @param string|null $dimensionValue
    * @return array
    */
   public function getTrend(
      string $metricName,
      string $periodType,
      string $startDate,
      string $endDate,
      ?string $dimension = null,
      ?string $dimensionValue = null
   ): array {
      $sql = "SELECT * FROM {$this->table}
              WHERE metric_name = :metric_name
              AND period_type = :period_type
              AND period_start >= :start_date
              AND period_end <= :end_date";

      $params = [
         'metric_name' => $metricName,
         'period_type' => $periodType,
         'start_date' => $startDate,
         'end_date' => $endDate
      ];

      if ($dimension !== null) {
         $sql .= " AND dimension = :dimension";
         $params['dimension'] = $dimension;
      }

      if ($dimensionValue !== null) {
         $sql .= " AND dimension_value = :dimension_value";
         $params['dimension_value'] = $dimensionValue;
      }

      $sql .= " ORDER BY period_start ASC";

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $metrics = $stmt->getAll();

      return array_map(function ($metric) {
         return $this->mapToObject((array) $metric);
      }, $metrics);
   }
}
