<?php

declare(strict_types=1);

namespace App\Modules\AuditLog\Models;

use System\Database\Database;
use System\Http\Request;
use System\Log\Log;
use System\Exception\SystemException;

class AuditLogModel {
   public function __construct(
      private Database $database,
      private Request $request,
      private Log $logger
   ) {
   }

   /** Denetim Loglama
    * $logToFile true olması halinde dosyaya loglanır. Denenmedi
    *
    * @param int|null $userId
    * @param string $eventType
    * @param array|null $details
    * @param bool $logToFile
    * @throws SystemException
    */
   public function logEvent(
      ?int $userId,
      string $eventType,
      ?array $details = null,
      bool $logToFile = false
   ): void {
      try {
         // Veritabanına Kayıt
         $this->database->prepare(
            'INSERT INTO audit_logs
                (user_id, event_type, details, ip_address, user_agent)
                VALUES (:user_id, :event_type, :details, :ip, :ua)
            ')->execute([
            'user_id' => $userId,
            'event_type' => $eventType,
            'details' => $details ? json_encode($this->sanitizeDetails($details)) : null,
            'ip' => $this->request->ip(),
            'ua' => $this->request->server('HTTP_USER_AGENT') ?? 'Unknown'
         ]);

         // Dosyaya Loglama (Opsiyonel)
         //COMBAK çalışıyor mu?
         if ($logToFile) {
            $this->logger->info('Audit Action', [
               'user_id' => $userId,
               'event_type' => $eventType,
               'ip' => $this->request->ip()
            ]);
         }
      } catch (\PDOException $e) {
         $this->logger->critical('Audit log failed: ' . $e->getMessage());
         throw new SystemException(' Audit logging service unavailable');
      }
   }
   /** Hassas verileri maskele
    *
    * @param array $details
    * @return array
    */
   private function sanitizeDetails(array $details): array {
      $sensitiveKeys = ['password', 'token', 'credit_card'];
      foreach ($sensitiveKeys as $key) {
         if (isset($details[$key])) {
            $details[$key] = '***MASKED***';
         }
      }
      return $details;
   }

   public function getLogs(
      ?int $userId = null,
      ?string $actionType = null,
      int $limit = 100
   ): array {
      $query = 'SELECT * FROM audit_logs WHERE 1=1';
      $params = [];

      if ($userId) {
         $query .= ' AND user_id = :user_id';
         $params['user_id'] = $userId;
      }

      if ($actionType) {
         $query .= ' AND event_type = :action';
         $params['action'] = $actionType;
      }

      $query .= ' ORDER BY created_at DESC LIMIT :limit';
      $params['limit'] = $limit;

      return $this->database->prepare($query)->execute($params)->getAll();
   }
}
