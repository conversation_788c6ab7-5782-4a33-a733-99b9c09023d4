2025-08-06 19:55:44,723 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-06 19:55:44,724 - freqtrade.loggers - INFO - Logfile configured
2025-08-06 19:55:44,726 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-06 19:55:44,729 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-06 19:55:44,730 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-06 19:55:44,732 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-06 19:55:44,735 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-06 19:55:44,737 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-06 19:55:44,830 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-06 19:55:44,835 - freqtrade.configuration.directory_operations - INFO - Created data directory: None
2025-08-06 19:55:44,836 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-06 19:55:44,840 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-06 19:55:44,865 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-06 19:55:44,866 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-06 19:55:44,869 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-06 19:55:44,871 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.96
2025-08-06 19:55:44,928 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-06 19:55:48,301 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-06 19:55:48,349 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-08-06 19:55:48,352 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-06 19:55:48,353 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-06 19:55:48,355 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-06 19:55:48,357 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-06 19:55:48,359 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-06 19:55:48,361 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-08-06 19:55:48,363 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-08-06 19:55:48,365 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-08-06 19:55:48,366 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-08-06 19:55:48,368 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-08-06 19:55:48,369 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-08-06 19:55:48,371 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-06 19:55:48,373 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-06 19:55:48,375 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-08-06 19:55:48,377 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-06 19:55:48,378 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-06 19:55:48,380 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-06 19:55:48,382 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-08-06 19:55:48,383 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-06 19:55:48,385 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-06 19:55:48,386 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-06 19:55:48,388 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-06 19:55:48,390 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-06 19:55:48,392 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-06 19:55:48,393 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-06 19:55:48,395 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-06 19:55:48,396 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-06 19:55:48,398 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-06 19:55:48,399 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-06 19:55:48,814 - freqtrade.wallets - INFO - Wallets synced.
2025-08-06 19:55:49,109 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-06 19:55:49,280 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-06 19:55:49,585 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-08-06 19:55:49,586 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-06 19:55:49,617 - uvicorn.error - INFO - Started server process [1]
2025-08-06 19:55:49,618 - uvicorn.error - INFO - Waiting for application startup.
2025-08-06 19:55:49,620 - uvicorn.error - INFO - Application startup complete.
2025-08-06 19:55:49,624 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-06 19:55:49,653 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist VolumePairList from '/freqtrade/freqtrade/plugins/pairlist/VolumePairList.py'...
2025-08-06 19:55:49,654 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-06 19:55:50,487 - VolumePairList - INFO - Pair BNB/USDT in your blacklist. Removing it from whitelist...
2025-08-06 19:55:50,542 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 20 pairs: ['ETH/USDT', 'USDC/USDT', 'BTC/USDT', 'XRP/USDT', 'SOL/USDT', 'PROVE/USDT', 'FDUSD/USDT', 'DOGE/USDT', 'ENA/USDT', 'TRX/USDT', 'SUI/USDT', 'TOWNS/USDT', 'PEPE/USDT', 'LTC/USDT', 'PENGU/USDT', 'ADA/USDT', 'HBAR/USDT', 'BONK/USDT', 'ILV/USDT', 'AVAX/USDT']
2025-08-06 19:55:50,544 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.89s
2025-08-06 19:55:50,546 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-06 19:55:50,548 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-08-06 19:55:50,550 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-08-06 19:55:50,552 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-06 19:55:50,554 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-08-06 19:55:50,556 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-08-06 19:55:50,558 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-06 19:55:50,559 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-08-06 19:55:50,561 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-06 19:55:50,563 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-06 19:55:50,608 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-06 19:55:50,609 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-08-06 19:55:50,611 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'VolumePairList': 'VolumePairList - top 20 volume pairs.'}]"}
2025-08-06 19:55:55,621 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 19:56:55,621 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 19:58:00,620 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 19:59:05,620 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:00:01,025 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.308). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,027 - freqtrade.exchange.exchange_ws - WARNING - TRX/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.349). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,029 - freqtrade.exchange.exchange_ws - WARNING - SUI/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.291). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,030 - freqtrade.exchange.exchange_ws - WARNING - FDUSD/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.291). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,032 - freqtrade.exchange.exchange_ws - WARNING - BONK/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.433). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,033 - freqtrade.exchange.exchange_ws - WARNING - ENA/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.583). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,035 - freqtrade.exchange.exchange_ws - WARNING - ADA/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.291). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,036 - freqtrade.exchange.exchange_ws - WARNING - USDC/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.435). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,038 - freqtrade.exchange.exchange_ws - WARNING - PENGU/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.881). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,040 - freqtrade.exchange.exchange_ws - WARNING - DOGE/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.561). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,041 - freqtrade.exchange.exchange_ws - WARNING - PEPE/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.291). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,043 - freqtrade.exchange.exchange_ws - WARNING - LTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.291). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,045 - freqtrade.exchange.exchange_ws - WARNING - HBAR/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.639). This usually suggests a problem with time synchronization.
2025-08-06 20:00:01,046 - freqtrade.exchange.exchange_ws - WARNING - SOL/USDT, 5m - Candle date > last refresh (2025-08-06T20:00:00 > 2025-08-06T19:59:59.779). This usually suggests a problem with time synchronization.
2025-08-06 20:00:06,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:01:06,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:02:11,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:03:16,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:04:16,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:05:01,019 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.307). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,021 - freqtrade.exchange.exchange_ws - WARNING - TRX/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.355). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,025 - freqtrade.exchange.exchange_ws - WARNING - SUI/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.233). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,026 - freqtrade.exchange.exchange_ws - WARNING - FDUSD/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.284). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,028 - freqtrade.exchange.exchange_ws - WARNING - BONK/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.428). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,030 - freqtrade.exchange.exchange_ws - WARNING - ENA/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.585). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,031 - freqtrade.exchange.exchange_ws - WARNING - ADA/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.304). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,033 - freqtrade.exchange.exchange_ws - WARNING - USDC/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.428). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,035 - freqtrade.exchange.exchange_ws - WARNING - PENGU/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.900). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,037 - freqtrade.exchange.exchange_ws - WARNING - DOGE/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.585). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,039 - freqtrade.exchange.exchange_ws - WARNING - PEPE/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.233). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,040 - freqtrade.exchange.exchange_ws - WARNING - LTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.204). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,042 - freqtrade.exchange.exchange_ws - WARNING - HBAR/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.635). This usually suggests a problem with time synchronization.
2025-08-06 20:05:01,044 - freqtrade.exchange.exchange_ws - WARNING - SOL/USDT, 5m - Candle date > last refresh (2025-08-06T20:05:00 > 2025-08-06T20:04:59.786). This usually suggests a problem with time synchronization.
2025-08-06 20:05:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:06:25,998 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:07:30,998 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:08:30,998 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:09:35,998 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:10:01,017 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.314). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,020 - freqtrade.exchange.exchange_ws - WARNING - TRX/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.362). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,021 - freqtrade.exchange.exchange_ws - WARNING - SUI/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.249). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,023 - freqtrade.exchange.exchange_ws - WARNING - FDUSD/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.298). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,025 - freqtrade.exchange.exchange_ws - WARNING - BONK/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.431). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,026 - freqtrade.exchange.exchange_ws - WARNING - ENA/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.586). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,030 - freqtrade.exchange.exchange_ws - WARNING - ADA/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.305). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,032 - freqtrade.exchange.exchange_ws - WARNING - USDC/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.444). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,033 - freqtrade.exchange.exchange_ws - WARNING - PENGU/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.923). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,035 - freqtrade.exchange.exchange_ws - WARNING - DOGE/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.571). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,037 - freqtrade.exchange.exchange_ws - WARNING - PEPE/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.249). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,038 - freqtrade.exchange.exchange_ws - WARNING - LTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.207). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,040 - freqtrade.exchange.exchange_ws - WARNING - HBAR/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.645). This usually suggests a problem with time synchronization.
2025-08-06 20:10:01,042 - freqtrade.exchange.exchange_ws - WARNING - SOL/USDT, 5m - Candle date > last refresh (2025-08-06T20:10:00 > 2025-08-06T20:09:59.795). This usually suggests a problem with time synchronization.
2025-08-06 20:10:35,999 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:11:40,999 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:12:41,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:12:45,346 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-08-06 20:12:45,348 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-08-06 20:12:45,350 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-08-06 20:12:45,354 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-08-06 20:12:45,356 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-08-06 20:12:45,357 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-08-06 20:12:45,404 - uvicorn.error - INFO - Shutting down
2025-08-06 20:12:45,506 - uvicorn.error - INFO - Waiting for application shutdown.
2025-08-06 20:12:45,508 - uvicorn.error - INFO - Application shutdown complete.
2025-08-06 20:12:45,510 - uvicorn.error - INFO - Finished server process [1]
2025-08-06 20:12:45,512 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-08-06 20:12:45,514 - freqtrade.exchange.exchange_ws - INFO - Resetting WS connections.
2025-08-06 20:12:45,516 - freqtrade.exchange.exchange_ws - INFO - BONK/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,518 - freqtrade.exchange.exchange_ws - INFO - ETH/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,520 - freqtrade.exchange.exchange_ws - INFO - ENA/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,521 - freqtrade.exchange.exchange_ws - INFO - AVAX/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,523 - freqtrade.exchange.exchange_ws - INFO - ADA/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,526 - freqtrade.exchange.exchange_ws - INFO - BTC/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,527 - freqtrade.exchange.exchange_ws - INFO - USDC/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,528 - freqtrade.exchange.exchange_ws - INFO - PENGU/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,530 - freqtrade.exchange.exchange_ws - INFO - DOGE/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,532 - freqtrade.exchange.exchange_ws - INFO - ILV/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,534 - freqtrade.exchange.exchange_ws - INFO - PEPE/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,535 - freqtrade.exchange.exchange_ws - INFO - XRP/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,536 - freqtrade.exchange.exchange_ws - INFO - LTC/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,538 - freqtrade.exchange.exchange_ws - INFO - TRX/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,539 - freqtrade.exchange.exchange_ws - INFO - PROVE/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,541 - freqtrade.exchange.exchange_ws - INFO - SUI/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,542 - freqtrade.exchange.exchange_ws - INFO - TOWNS/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,544 - freqtrade.exchange.exchange_ws - INFO - FDUSD/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,545 - freqtrade.exchange.exchange_ws - INFO - SOL/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,547 - freqtrade.exchange.exchange_ws - INFO - HBAR/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:12:45,552 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,555 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,557 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,560 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,562 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,564 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,567 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,569 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,571 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,574 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,576 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,578 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,581 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,583 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,586 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,589 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,591 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,593 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,596 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:45,598 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:12:46,568 - freqtrade - INFO - SIGINT received, aborting ...
2025-08-06 20:12:46,847 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14389' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,851 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14390' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,852 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14391' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,854 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14392' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,856 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14393' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,858 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14394' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,859 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14395' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,861 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14396' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,863 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14397' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,865 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14398' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,866 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14399' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,867 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14400' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,869 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14401' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,870 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14402' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,872 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14403' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,874 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14404' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,875 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14405' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,877 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14406' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:12:46,879 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-14407' coro=<Throttler.looper() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/throttler.py:21> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:13:00,250 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-06 20:13:00,252 - freqtrade.loggers - INFO - Logfile configured
2025-08-06 20:13:00,254 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-06 20:13:00,256 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-06 20:13:00,258 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-06 20:13:00,260 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-06 20:13:00,262 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-06 20:13:00,263 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-06 20:13:00,358 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-06 20:13:00,363 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-06 20:13:00,366 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-06 20:13:00,386 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-06 20:13:00,388 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-06 20:13:00,391 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-06 20:13:00,392 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.96
2025-08-06 20:13:00,456 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-06 20:13:03,853 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-06 20:13:04,510 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-08-06 20:13:04,512 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-06 20:13:04,515 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-06 20:13:04,516 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-06 20:13:04,517 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-06 20:13:04,519 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-06 20:13:04,521 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-08-06 20:13:04,522 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-08-06 20:13:04,524 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-08-06 20:13:04,526 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-08-06 20:13:04,528 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-08-06 20:13:04,529 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-08-06 20:13:04,531 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-06 20:13:04,533 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-06 20:13:04,534 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-08-06 20:13:04,536 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-06 20:13:04,537 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-06 20:13:04,540 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-06 20:13:04,541 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-08-06 20:13:04,543 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-06 20:13:04,545 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-06 20:13:04,547 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-06 20:13:04,549 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-06 20:13:04,551 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-06 20:13:04,552 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-06 20:13:04,554 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-06 20:13:04,556 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-06 20:13:04,558 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-06 20:13:04,559 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-06 20:13:04,561 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-06 20:13:05,030 - freqtrade.wallets - INFO - Wallets synced.
2025-08-06 20:13:05,341 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-06 20:13:05,534 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-06 20:13:05,841 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-08-06 20:13:05,843 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-06 20:13:05,870 - uvicorn.error - INFO - Started server process [1]
2025-08-06 20:13:05,873 - uvicorn.error - INFO - Waiting for application startup.
2025-08-06 20:13:05,876 - uvicorn.error - INFO - Application startup complete.
2025-08-06 20:13:05,878 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-06 20:13:05,915 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist VolumePairList from '/freqtrade/freqtrade/plugins/pairlist/VolumePairList.py'...
2025-08-06 20:13:05,916 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-06 20:13:05,992 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info'], ['profit_long'], ['profit_short']]
2025-08-06 20:13:06,701 - VolumePairList - INFO - Pair BNB/USDT in your blacklist. Removing it from whitelist...
2025-08-06 20:13:06,772 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 20 pairs: ['ETH/USDT', 'USDC/USDT', 'BTC/USDT', 'XRP/USDT', 'SOL/USDT', 'PROVE/USDT', 'FDUSD/USDT', 'DOGE/USDT', 'ENA/USDT', 'TRX/USDT', 'SUI/USDT', 'TOWNS/USDT', 'PEPE/USDT', 'LTC/USDT', 'PENGU/USDT', 'ADA/USDT', 'HBAR/USDT', 'BONK/USDT', 'ILV/USDT', 'AVAX/USDT']
2025-08-06 20:13:06,774 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.86s
2025-08-06 20:13:06,776 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-06 20:13:06,778 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-08-06 20:13:06,780 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-08-06 20:13:06,781 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-06 20:13:06,783 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-08-06 20:13:06,785 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-08-06 20:13:06,786 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-06 20:13:06,788 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-08-06 20:13:06,789 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-06 20:13:06,791 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-06 20:13:06,846 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-06 20:13:06,849 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-08-06 20:13:06,854 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'VolumePairList': 'VolumePairList - top 20 volume pairs.'}]"}
2025-08-06 20:13:08,878 - freqtrade.rpc.telegram - WARNING - Telegram NetworkError: Chat not found! Trying one more time.
2025-08-06 20:13:08,895 - freqtrade.rpc.telegram - WARNING - Telegram NetworkError: Chat not found! Trying one more time.
2025-08-06 20:13:08,899 - freqtrade.rpc.telegram - WARNING - Telegram NetworkError: Chat not found! Trying one more time.
2025-08-06 20:13:08,919 - freqtrade.rpc.telegram - WARNING - Telegram NetworkError: Chat not found! Trying one more time.
2025-08-06 20:13:08,937 - telegram.ext.Application - INFO - Application started
2025-08-06 20:13:09,016 - freqtrade.rpc.telegram - WARNING - TelegramError: Chat not found! Giving up on that message.
2025-08-06 20:13:09,046 - freqtrade.rpc.telegram - WARNING - TelegramError: Chat not found! Giving up on that message.
2025-08-06 20:13:09,066 - freqtrade.rpc.telegram - WARNING - TelegramError: Chat not found! Giving up on that message.
2025-08-06 20:13:09,072 - freqtrade.rpc.telegram - WARNING - TelegramError: Chat not found! Giving up on that message.
2025-08-06 20:13:11,872 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:14:11,873 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:14:33,355 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 112, in wrapper
    chat_id = int(self._config["telegram"]["chat_id"])
ValueError: invalid literal for int() with base 10: '+g9i-atpilxVkZWQ8'
2025-08-06 20:15:01,026 - freqtrade.exchange.exchange_ws - WARNING - LTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.353). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,027 - freqtrade.exchange.exchange_ws - WARNING - FDUSD/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.353). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,029 - freqtrade.exchange.exchange_ws - WARNING - BONK/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.436). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,030 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.353). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,032 - freqtrade.exchange.exchange_ws - WARNING - SUI/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.353). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,034 - freqtrade.exchange.exchange_ws - WARNING - HBAR/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.645). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,036 - freqtrade.exchange.exchange_ws - WARNING - PEPE/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.353). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,038 - freqtrade.exchange.exchange_ws - WARNING - ENA/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.596). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,039 - freqtrade.exchange.exchange_ws - WARNING - TRX/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.374). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,042 - freqtrade.exchange.exchange_ws - WARNING - ADA/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.353). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,044 - freqtrade.exchange.exchange_ws - WARNING - PENGU/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.903). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,047 - freqtrade.exchange.exchange_ws - WARNING - SOL/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.802). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,050 - freqtrade.exchange.exchange_ws - WARNING - DOGE/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.582). This usually suggests a problem with time synchronization.
2025-08-06 20:15:01,052 - freqtrade.exchange.exchange_ws - WARNING - USDC/USDT, 5m - Candle date > last refresh (2025-08-06T20:15:00 > 2025-08-06T20:14:59.440). This usually suggests a problem with time synchronization.
2025-08-06 20:15:15,555 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 112, in wrapper
    chat_id = int(self._config["telegram"]["chat_id"])
ValueError: invalid literal for int() with base 10: '+g9i-atpilxVkZWQ8'
2025-08-06 20:15:16,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:15:21,994 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 112, in wrapper
    chat_id = int(self._config["telegram"]["chat_id"])
ValueError: invalid literal for int() with base 10: '+g9i-atpilxVkZWQ8'
2025-08-06 20:16:16,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:17:16,003 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:18:09,093 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 112, in wrapper
    chat_id = int(self._config["telegram"]["chat_id"])
ValueError: invalid literal for int() with base 10: '+g9i-atpilxVkZWQ8'
2025-08-06 20:18:16,003 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:19:16,004 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:19:30,597 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 112, in wrapper
    chat_id = int(self._config["telegram"]["chat_id"])
ValueError: invalid literal for int() with base 10: '+g9i-atpilxVkZWQ8'
2025-08-06 20:19:33,177 - telegram.ext.Application - ERROR - No error handlers are registered, logging exception.
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_application.py", line 1311, in process_update
    await coroutine
  File "/home/<USER>/.local/lib/python3.13/site-packages/telegram/ext/_handlers/basehandler.py", line 158, in handle_update
    return await self.callback(update, context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/freqtrade/freqtrade/rpc/telegram.py", line 112, in wrapper
    chat_id = int(self._config["telegram"]["chat_id"])
ValueError: invalid literal for int() with base 10: '+g9i-atpilxVkZWQ8'
2025-08-06 20:20:01,019 - freqtrade.exchange.exchange_ws - WARNING - LTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.239). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,020 - freqtrade.exchange.exchange_ws - WARNING - FDUSD/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.316). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,022 - freqtrade.exchange.exchange_ws - WARNING - BONK/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.455). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,024 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.330). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,025 - freqtrade.exchange.exchange_ws - WARNING - SUI/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.262). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,027 - freqtrade.exchange.exchange_ws - WARNING - HBAR/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.679). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,029 - freqtrade.exchange.exchange_ws - WARNING - PEPE/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.262). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,030 - freqtrade.exchange.exchange_ws - WARNING - BTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.028). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,034 - freqtrade.exchange.exchange_ws - WARNING - ENA/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.609). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,036 - freqtrade.exchange.exchange_ws - WARNING - TRX/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.373). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,038 - freqtrade.exchange.exchange_ws - WARNING - ADA/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.325). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,040 - freqtrade.exchange.exchange_ws - WARNING - PENGU/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.924). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,041 - freqtrade.exchange.exchange_ws - WARNING - SOL/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.807). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,043 - freqtrade.exchange.exchange_ws - WARNING - DOGE/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.608). This usually suggests a problem with time synchronization.
2025-08-06 20:20:01,045 - freqtrade.exchange.exchange_ws - WARNING - USDC/USDT, 5m - Candle date > last refresh (2025-08-06T20:20:00 > 2025-08-06T20:19:59.455). This usually suggests a problem with time synchronization.
2025-08-06 20:20:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:21:21,002 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:23:05,327 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-06 20:23:05,330 - freqtrade.loggers - INFO - Logfile configured
2025-08-06 20:23:05,331 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-06 20:23:05,334 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-06 20:23:05,336 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-06 20:23:05,337 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-06 20:23:05,339 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/tradesv3.sqlite"
2025-08-06 20:23:05,340 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-06 20:23:05,448 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-06 20:23:05,452 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-06 20:23:05,455 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-06 20:23:05,479 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-06 20:23:05,481 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-06 20:23:05,484 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-06 20:23:05,486 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.96
2025-08-06 20:23:05,558 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-06 20:23:09,539 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-06 20:23:10,203 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-08-06 20:23:10,206 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-06 20:23:10,208 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-06 20:23:10,209 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-06 20:23:10,211 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-06 20:23:10,213 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-06 20:23:10,214 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-08-06 20:23:10,217 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-08-06 20:23:10,218 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-08-06 20:23:10,221 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-08-06 20:23:10,223 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-08-06 20:23:10,224 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-08-06 20:23:10,226 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-06 20:23:10,227 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-06 20:23:10,228 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-08-06 20:23:10,230 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-06 20:23:10,232 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-06 20:23:10,233 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-06 20:23:10,235 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-08-06 20:23:10,236 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-06 20:23:10,238 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-06 20:23:10,240 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-06 20:23:10,241 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-06 20:23:10,243 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-06 20:23:10,246 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-06 20:23:10,247 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-06 20:23:10,248 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-06 20:23:10,250 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-06 20:23:10,252 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-06 20:23:10,254 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-06 20:23:10,375 - freqtrade.wallets - INFO - Wallets synced.
2025-08-06 20:23:10,672 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-06 20:23:10,852 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-06 20:23:11,141 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-08-06 20:23:11,143 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-06 20:23:11,166 - uvicorn.error - INFO - Started server process [1]
2025-08-06 20:23:11,167 - uvicorn.error - INFO - Waiting for application startup.
2025-08-06 20:23:11,170 - uvicorn.error - INFO - Application startup complete.
2025-08-06 20:23:11,172 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-06 20:23:11,208 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist VolumePairList from '/freqtrade/freqtrade/plugins/pairlist/VolumePairList.py'...
2025-08-06 20:23:11,209 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-06 20:23:11,293 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info'], ['profit_long'], ['profit_short']]
2025-08-06 20:23:11,543 - telegram.ext.Application - INFO - Application started
2025-08-06 20:23:12,019 - VolumePairList - INFO - Pair BNB/USDT in your blacklist. Removing it from whitelist...
2025-08-06 20:23:12,071 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 20 pairs: ['ETH/USDT', 'USDC/USDT', 'BTC/USDT', 'XRP/USDT', 'SOL/USDT', 'PROVE/USDT', 'FDUSD/USDT', 'DOGE/USDT', 'ENA/USDT', 'TRX/USDT', 'SUI/USDT', 'TOWNS/USDT', 'LTC/USDT', 'PEPE/USDT', 'PENGU/USDT', 'ADA/USDT', 'HBAR/USDT', 'BONK/USDT', 'ILV/USDT', 'AVAX/USDT']
2025-08-06 20:23:12,073 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.86s
2025-08-06 20:23:12,075 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-06 20:23:12,077 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-08-06 20:23:12,079 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-08-06 20:23:12,080 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-06 20:23:12,082 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-08-06 20:23:12,083 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-08-06 20:23:12,085 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-06 20:23:12,087 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-08-06 20:23:12,089 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-06 20:23:12,092 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-06 20:23:12,114 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-06 20:23:12,115 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-08-06 20:23:12,120 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'VolumePairList': 'VolumePairList - top 20 volume pairs.'}]"}
2025-08-06 20:23:17,144 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:23:47,773 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-08-06 20:23:47,775 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-08-06 20:23:47,777 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-08-06 20:23:47,784 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-08-06 20:23:47,787 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-08-06 20:23:47,788 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-08-06 20:23:47,883 - uvicorn.error - INFO - Shutting down
2025-08-06 20:23:47,984 - uvicorn.error - INFO - Waiting for application shutdown.
2025-08-06 20:23:47,986 - uvicorn.error - INFO - Application shutdown complete.
2025-08-06 20:23:47,987 - uvicorn.error - INFO - Finished server process [1]
2025-08-06 20:23:47,989 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-08-06 20:23:48,201 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-06 20:23:48,202 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-06 20:23:51,629 - freqtrade.exchange.exchange_ws - INFO - Resetting WS connections.
2025-08-06 20:23:51,631 - freqtrade.exchange.exchange_ws - INFO - FDUSD/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,634 - freqtrade.exchange.exchange_ws - INFO - USDC/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,636 - freqtrade.exchange.exchange_ws - INFO - PEPE/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,638 - freqtrade.exchange.exchange_ws - INFO - HBAR/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,640 - freqtrade.exchange.exchange_ws - INFO - SOL/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,642 - freqtrade.exchange.exchange_ws - INFO - XRP/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,644 - freqtrade.exchange.exchange_ws - INFO - BTC/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,646 - freqtrade.exchange.exchange_ws - INFO - LTC/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,648 - freqtrade.exchange.exchange_ws - INFO - TOWNS/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,650 - freqtrade.exchange.exchange_ws - INFO - AVAX/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,652 - freqtrade.exchange.exchange_ws - INFO - DOGE/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,653 - freqtrade.exchange.exchange_ws - INFO - ENA/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,655 - freqtrade.exchange.exchange_ws - INFO - TRX/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,656 - freqtrade.exchange.exchange_ws - INFO - BONK/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,659 - freqtrade.exchange.exchange_ws - INFO - ILV/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,662 - freqtrade.exchange.exchange_ws - INFO - PENGU/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,665 - freqtrade.exchange.exchange_ws - INFO - ETH/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,668 - freqtrade.exchange.exchange_ws - INFO - PROVE/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,670 - freqtrade.exchange.exchange_ws - INFO - ADA/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,672 - freqtrade.exchange.exchange_ws - INFO - SUI/USDT, 5m, spot - Task finished - cancelled
2025-08-06 20:23:51,681 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,686 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,689 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,692 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,695 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,698 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,701 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,703 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,706 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,709 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,711 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,714 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,718 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,721 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,725 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,728 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,732 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,735 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,739 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:51,742 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-06 20:23:52,692 - freqtrade - INFO - SIGINT received, aborting ...
2025-08-06 20:23:53,034 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-637' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,040 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-638' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,042 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-639' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,044 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-640' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,046 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-641' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,048 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-642' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,050 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-643' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,051 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-644' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,053 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-645' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,055 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-646' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,057 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-647' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,059 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-648' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,061 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-649' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,064 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-650' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,066 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-651' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,068 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-652' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,070 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-653' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,071 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-654' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:23:53,074 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-655' coro=<Throttler.looper() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/throttler.py:21> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-06 20:25:38,012 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-06 20:25:38,013 - freqtrade.loggers - INFO - Logfile configured
2025-08-06 20:25:38,016 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-06 20:25:38,019 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-06 20:25:38,020 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-06 20:25:38,021 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-06 20:25:38,023 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/database/tradesv3.sqlite"
2025-08-06 20:25:38,024 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-06 20:25:38,116 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-06 20:25:38,119 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-06 20:25:38,122 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-06 20:25:38,146 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-06 20:25:38,148 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-06 20:25:38,150 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-06 20:25:38,152 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.96
2025-08-06 20:25:38,217 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-06 20:25:41,621 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-06 20:25:42,276 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-08-06 20:25:42,278 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-06 20:25:42,281 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-06 20:25:42,282 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-06 20:25:42,283 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-06 20:25:42,285 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-06 20:25:42,286 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-08-06 20:25:42,289 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-08-06 20:25:42,290 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-08-06 20:25:42,292 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-08-06 20:25:42,294 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-08-06 20:25:42,295 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-08-06 20:25:42,298 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-06 20:25:42,300 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-06 20:25:42,301 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-08-06 20:25:42,304 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-06 20:25:42,306 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-06 20:25:42,308 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-06 20:25:42,310 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-08-06 20:25:42,312 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-06 20:25:42,313 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-06 20:25:42,315 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-06 20:25:42,317 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-06 20:25:42,319 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-06 20:25:42,321 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-06 20:25:42,323 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-06 20:25:42,325 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-06 20:25:42,327 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-06 20:25:42,328 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-06 20:25:42,330 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-06 20:25:42,496 - freqtrade.wallets - INFO - Wallets synced.
2025-08-06 20:25:42,758 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-06 20:25:42,953 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-06 20:25:43,244 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-08-06 20:25:43,246 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-06 20:25:43,275 - uvicorn.error - INFO - Started server process [1]
2025-08-06 20:25:43,277 - uvicorn.error - INFO - Waiting for application startup.
2025-08-06 20:25:43,281 - uvicorn.error - INFO - Application startup complete.
2025-08-06 20:25:43,284 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-06 20:25:43,321 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist VolumePairList from '/freqtrade/freqtrade/plugins/pairlist/VolumePairList.py'...
2025-08-06 20:25:43,323 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-06 20:25:43,387 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info'], ['profit_long'], ['profit_short']]
2025-08-06 20:25:43,611 - telegram.ext.Application - INFO - Application started
2025-08-06 20:25:44,463 - VolumePairList - INFO - Pair BNB/USDT in your blacklist. Removing it from whitelist...
2025-08-06 20:25:44,520 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 20 pairs: ['ETH/USDT', 'USDC/USDT', 'BTC/USDT', 'XRP/USDT', 'SOL/USDT', 'PROVE/USDT', 'FDUSD/USDT', 'DOGE/USDT', 'ENA/USDT', 'TRX/USDT', 'SUI/USDT', 'TOWNS/USDT', 'LTC/USDT', 'PEPE/USDT', 'PENGU/USDT', 'ADA/USDT', 'HBAR/USDT', 'BONK/USDT', 'ILV/USDT', 'AVAX/USDT']
2025-08-06 20:25:44,521 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 1.20s
2025-08-06 20:25:44,523 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-06 20:25:44,524 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-08-06 20:25:44,526 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-08-06 20:25:44,527 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-06 20:25:44,529 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-08-06 20:25:44,530 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-08-06 20:25:44,532 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-06 20:25:44,534 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-08-06 20:25:44,535 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-06 20:25:44,538 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-06 20:25:44,567 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-06 20:25:44,569 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-08-06 20:25:44,575 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'VolumePairList': 'VolumePairList - top 20 volume pairs.'}]"}
2025-08-06 20:25:49,601 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:26:49,603 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:27:49,605 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:28:49,605 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:29:49,606 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:30:01,022 - freqtrade.exchange.exchange_ws - WARNING - ENA/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.629). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,024 - freqtrade.exchange.exchange_ws - WARNING - USDC/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.461). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,025 - freqtrade.exchange.exchange_ws - WARNING - SUI/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.342). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,027 - freqtrade.exchange.exchange_ws - WARNING - PEPE/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.342). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,029 - freqtrade.exchange.exchange_ws - WARNING - PENGU/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.927). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,031 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.354). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,033 - freqtrade.exchange.exchange_ws - WARNING - SOL/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.854). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,035 - freqtrade.exchange.exchange_ws - WARNING - LTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.342). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,037 - freqtrade.exchange.exchange_ws - WARNING - HBAR/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.673). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,040 - freqtrade.exchange.exchange_ws - WARNING - BONK/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.454). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,042 - freqtrade.exchange.exchange_ws - WARNING - BTC/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.053). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,044 - freqtrade.exchange.exchange_ws - WARNING - FDUSD/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.342). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,046 - freqtrade.exchange.exchange_ws - WARNING - ETH/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.053). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,047 - freqtrade.exchange.exchange_ws - WARNING - DOGE/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.607). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,050 - freqtrade.exchange.exchange_ws - WARNING - ADA/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.342). This usually suggests a problem with time synchronization.
2025-08-06 20:30:01,052 - freqtrade.exchange.exchange_ws - WARNING - TRX/USDT, 5m - Candle date > last refresh (2025-08-06T20:30:00 > 2025-08-06T20:29:59.387). This usually suggests a problem with time synchronization.
2025-08-06 20:30:51,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:31:51,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:32:51,196 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:33:56,158 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:35:01,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:36:05,993 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:37:10,988 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:38:15,983 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:38:29,768 - uvicorn.error - INFO - 172.19.0.1:55662 - "WebSocket /api/v1/message/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGl0eSI6eyJ1IjoiZnEifSwiZXhwIjoxNzU0NTEzNjAxLCJpYXQiOjE3NTQ1MTI3MDEsInR5cGUiOiJhY2Nlc3MifQ.OfbIRgSGNxww3NZzoPTBr2xgzuXX6bMMtXGs-t64s7U" [accepted]
2025-08-06 20:38:29,770 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(e84247f7, ('172.19.0.1', 55662))
2025-08-06 20:38:29,773 - uvicorn.error - INFO - connection open
2025-08-06 20:39:20,977 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:40:21,001 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:40:57,871 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-08-06 20:40:57,872 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-08-06 20:40:57,875 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-08-06 20:40:57,881 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-08-06 20:40:57,883 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-08-06 20:40:57,887 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-08-06 20:45:34,039 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-06 20:45:34,040 - freqtrade.loggers - INFO - Logfile configured
2025-08-06 20:45:34,043 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-06 20:45:34,045 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-06 20:45:34,046 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-06 20:45:34,054 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-06 20:45:34,080 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/database/tradesv3.sqlite"
2025-08-06 20:45:34,083 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-06 20:45:34,202 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-06 20:45:34,206 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-06 20:45:34,209 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-06 20:45:34,233 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-06 20:45:34,235 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-06 20:45:34,238 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-06 20:45:34,239 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.96
2025-08-06 20:45:34,313 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-06 20:45:38,080 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-06 20:45:39,168 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-08-06 20:45:39,169 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-06 20:45:39,171 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-06 20:45:39,172 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-06 20:45:39,174 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-06 20:45:39,176 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-06 20:45:39,178 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-08-06 20:45:39,180 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-08-06 20:45:39,181 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-08-06 20:45:39,183 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-08-06 20:45:39,184 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-08-06 20:45:39,186 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-08-06 20:45:39,187 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-06 20:45:39,189 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-06 20:45:39,190 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-08-06 20:45:39,193 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-06 20:45:39,196 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-06 20:45:39,197 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-06 20:45:39,200 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-08-06 20:45:39,202 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-06 20:45:39,204 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-06 20:45:39,206 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-06 20:45:39,208 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-06 20:45:39,211 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-06 20:45:39,213 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-06 20:45:39,215 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-06 20:45:39,217 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-06 20:45:39,219 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-06 20:45:39,220 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-06 20:45:39,223 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-06 20:45:39,348 - freqtrade.wallets - INFO - Wallets synced.
2025-08-06 20:45:39,646 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-06 20:45:40,010 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-06 20:45:40,326 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-08-06 20:45:40,327 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-06 20:45:40,368 - uvicorn.error - INFO - Started server process [1]
2025-08-06 20:45:40,370 - uvicorn.error - INFO - Waiting for application startup.
2025-08-06 20:45:40,373 - uvicorn.error - INFO - Application startup complete.
2025-08-06 20:45:40,375 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-06 20:45:40,389 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-08-06 20:45:40,391 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-06 20:45:40,491 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring MATIC/USDT from whitelist. Market is not active.
2025-08-06 20:45:40,494 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info'], ['profit_long'], ['profit_short']]
2025-08-06 20:45:40,496 - freqtrade.plugins.pairlist.IPairList - INFO - Ignoring XMR/USDT from whitelist. Market is not active.
2025-08-06 20:45:40,508 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 12 pairs: ['ETH/USDT', 'BTC/USDT', 'XRP/USDT', 'SOL/USDT', 'DOGE/USDT', 'LTC/USDT', 'ADA/USDT', 'DOT/USDT', 'UNI/USDT', 'LINK/USDT', 'XLM/USDT', 'TRX/USDT']
2025-08-06 20:45:40,511 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.12s
2025-08-06 20:45:40,515 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-06 20:45:40,544 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-08-06 20:45:40,547 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-08-06 20:45:40,551 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-06 20:45:40,553 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-08-06 20:45:40,555 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-08-06 20:45:40,558 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-06 20:45:40,560 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-08-06 20:45:40,562 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-06 20:45:40,565 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-06 20:45:40,599 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-06 20:45:40,602 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-08-06 20:45:40,607 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-08-06 20:45:40,874 - telegram.ext.Application - INFO - Application started
2025-08-06 20:45:45,634 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:45:54,692 - uvicorn.error - INFO - 172.19.0.1:59264 - "WebSocket /api/v1/message/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGl0eSI6eyJ1IjoiZnEifSwiZXhwIjoxNzU0NTEzNjAxLCJpYXQiOjE3NTQ1MTI3MDEsInR5cGUiOiJhY2Nlc3MifQ.OfbIRgSGNxww3NZzoPTBr2xgzuXX6bMMtXGs-t64s7U" [accepted]
2025-08-06 20:45:54,694 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(143b2a7b, ('172.19.0.1', 59264))
2025-08-06 20:45:54,697 - uvicorn.error - INFO - connection open
2025-08-06 20:45:54,787 - uvicorn.error - INFO - 172.19.0.1:59266 - "WebSocket /api/v1/message/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZGVudGl0eSI6eyJ1IjoiZnEifSwiZXhwIjoxNzU0NTEzNjAxLCJpYXQiOjE3NTQ1MTI3MDEsInR5cGUiOiJhY2Nlc3MifQ.OfbIRgSGNxww3NZzoPTBr2xgzuXX6bMMtXGs-t64s7U" [accepted]
2025-08-06 20:45:54,793 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(99b3f4c8, ('172.19.0.1', 59266))
2025-08-06 20:45:54,797 - uvicorn.error - INFO - connection open
2025-08-06 20:46:50,632 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-06 20:47:20,999 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-08-06 20:47:21,001 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-08-06 20:47:21,004 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-08-06 20:47:21,011 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-08-06 20:47:21,013 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-08-06 20:47:21,016 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-08-07 08:22:24,877 - freqtrade.loggers - INFO - Enabling colorized output.
2025-08-07 08:22:24,878 - freqtrade.loggers - INFO - Logfile configured
2025-08-07 08:22:24,879 - freqtrade.loggers - INFO - Verbosity set to 0
2025-08-07 08:22:24,880 - freqtrade.configuration.configuration - INFO - Runmode set to dry_run.
2025-08-07 08:22:24,881 - freqtrade.configuration.configuration - INFO - Parameter --db-url detected ...
2025-08-07 08:22:24,882 - freqtrade.configuration.configuration - INFO - Dry run is enabled
2025-08-07 08:22:24,883 - freqtrade.configuration.configuration - INFO - Using DB: "sqlite:////freqtrade/user_data/database/tradesv3.sqlite"
2025-08-07 08:22:24,883 - freqtrade.configuration.configuration - INFO - Using max_open_trades: 3 ...
2025-08-07 08:22:25,017 - freqtrade.configuration.configuration - INFO - Using user-data directory: /freqtrade/user_data ...
2025-08-07 08:22:25,021 - freqtrade.configuration.configuration - INFO - Using data directory: /freqtrade/user_data/data/binance ...
2025-08-07 08:22:25,023 - freqtrade.exchange.check_exchange - INFO - Checking exchange...
2025-08-07 08:22:25,044 - freqtrade.exchange.check_exchange - INFO - Exchange "binance" is officially supported by the Freqtrade development team.
2025-08-07 08:22:25,046 - freqtrade.configuration.configuration - INFO - Using pairlist from configuration.
2025-08-07 08:22:25,047 - freqtrade.exchange.exchange - INFO - Instance is running with dry_run enabled
2025-08-07 08:22:25,049 - freqtrade.exchange.exchange - INFO - Using CCXT 4.4.96
2025-08-07 08:22:25,097 - freqtrade.exchange.exchange - INFO - Using Exchange "Binance"
2025-08-07 08:22:28,560 - freqtrade.resolvers.exchange_resolver - INFO - Using resolved exchange 'Binance'...
2025-08-07 08:22:29,932 - freqtrade.resolvers.iresolver - INFO - Using resolved strategy SampleStrategy from '/freqtrade/user_data/strategies/sample_strategy.py'...
2025-08-07 08:22:29,934 - freqtrade.strategy.hyper - INFO - Found no parameter file.
2025-08-07 08:22:29,936 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_currency' with value in config file: USDT.
2025-08-07 08:22:29,937 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'stake_amount' with value in config file: unlimited.
2025-08-07 08:22:29,938 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'unfilledtimeout' with value in config file: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}.
2025-08-07 08:22:29,940 - freqtrade.resolvers.strategy_resolver - INFO - Override strategy 'max_open_trades' with value in config file: 3.
2025-08-07 08:22:29,941 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using minimal_roi: {'60': 0.01, '30': 0.02, '0': 0.04}
2025-08-07 08:22:29,942 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using timeframe: 5m
2025-08-07 08:22:29,944 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stoploss: -0.1
2025-08-07 08:22:29,945 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop: False
2025-08-07 08:22:29,946 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_stop_positive_offset: 0.0
2025-08-07 08:22:29,947 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using trailing_only_offset_is_reached: False
2025-08-07 08:22:29,948 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_custom_stoploss: False
2025-08-07 08:22:29,950 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using process_only_new_candles: True
2025-08-07 08:22:29,952 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_types: {'entry': 'limit', 'exit': 'limit', 'stoploss': 'market', 'stoploss_on_exchange': False}
2025-08-07 08:22:29,953 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using order_time_in_force: {'entry': 'GTC', 'exit': 'GTC'}
2025-08-07 08:22:29,954 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_currency: USDT
2025-08-07 08:22:29,955 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using stake_amount: unlimited
2025-08-07 08:22:29,956 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using startup_candle_count: 200
2025-08-07 08:22:29,958 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using unfilledtimeout: {'entry': 10, 'exit': 10, 'exit_timeout_count': 0, 'unit': 'minutes'}
2025-08-07 08:22:29,959 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using use_exit_signal: True
2025-08-07 08:22:29,961 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_only: False
2025-08-07 08:22:29,962 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_roi_if_entry_signal: False
2025-08-07 08:22:29,963 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using exit_profit_offset: 0.0
2025-08-07 08:22:29,964 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using disable_dataframe_checks: False
2025-08-07 08:22:29,966 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using ignore_buying_expired_candle_after: 0
2025-08-07 08:22:29,967 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using position_adjustment_enable: False
2025-08-07 08:22:29,968 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_entry_position_adjustment: -1
2025-08-07 08:22:29,969 - freqtrade.resolvers.strategy_resolver - INFO - Strategy using max_open_trades: 3
2025-08-07 08:22:29,970 - freqtrade.configuration.config_validation - INFO - Validating configuration ...
2025-08-07 08:22:30,120 - freqtrade.wallets - INFO - Wallets synced.
2025-08-07 08:22:30,369 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.telegram ...
2025-08-07 08:22:30,713 - freqtrade.rpc.rpc_manager - INFO - Enabling rpc.api_server
2025-08-07 08:22:31,048 - freqtrade.rpc.api_server.webserver - INFO - Starting HTTP Server at 0.0.0.0:8080
2025-08-07 08:22:31,049 - freqtrade.rpc.api_server.webserver - INFO - Starting Local Rest Server.
2025-08-07 08:22:31,085 - uvicorn.error - INFO - Started server process [1]
2025-08-07 08:22:31,086 - uvicorn.error - INFO - Waiting for application startup.
2025-08-07 08:22:31,088 - uvicorn.error - INFO - Application startup complete.
2025-08-07 08:22:31,090 - uvicorn.error - INFO - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-08-07 08:22:31,101 - freqtrade.resolvers.iresolver - INFO - Using resolved pairlist StaticPairList from '/freqtrade/freqtrade/plugins/pairlist/StaticPairList.py'...
2025-08-07 08:22:31,102 - freqtrade.freqtradebot - INFO - Starting initial pairlist refresh
2025-08-07 08:22:31,130 - freqtrade.plugins.pairlistmanager - INFO - Whitelist with 3 pairs: ['ETH/USDT', 'BTC/USDT', 'XRP/USDT']
2025-08-07 08:22:31,132 - freqtrade.freqtradebot - INFO - Initial Pairlist refresh took 0.03s
2025-08-07 08:22:31,133 - freqtrade.strategy.hyper - INFO - No params for buy found, using default values.
2025-08-07 08:22:31,134 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): buy_rsi = 30
2025-08-07 08:22:31,135 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): exit_short_rsi = 30
2025-08-07 08:22:31,137 - freqtrade.strategy.hyper - INFO - No params for sell found, using default values.
2025-08-07 08:22:31,138 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): sell_rsi = 70
2025-08-07 08:22:31,139 - freqtrade.strategy.hyper - INFO - Strategy Parameter(default): short_rsi = 70
2025-08-07 08:22:31,140 - freqtrade.strategy.hyper - INFO - No params for protection found, using default values.
2025-08-07 08:22:31,141 - freqtrade.plugins.protectionmanager - INFO - No protection Handlers defined.
2025-08-07 08:22:31,142 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'running'}
2025-08-07 08:22:31,144 - freqtrade.worker - INFO - Changing state to: RUNNING
2025-08-07 08:22:31,158 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': warning, 'status': 'Dry run is enabled. All trades are simulated.'}
2025-08-07 08:22:31,160 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "*Exchange:* `binance`\n*Stake per trade:* `unlimited USDT`\n*Minimum ROI:* `{'60': 0.01, '30': 0.02, '0': 0.04}`\n*Stoploss:* `-0.1`\n*Position adjustment:* `Off`\n*Timeframe:* `5m`\n*Strategy:* `SampleStrategy`"}
2025-08-07 08:22:31,162 - freqtrade.rpc.telegram - INFO - rpc.telegram is listening for following commands: [['status'], ['profit'], ['balance'], ['start'], ['stop'], ['forceexit', 'forcesell', 'fx'], ['forcebuy', 'forcelong'], ['forceshort'], ['reload_trade'], ['trades'], ['delete'], ['cancel_open_order', 'coo'], ['performance'], ['buys', 'entries'], ['exits', 'sells'], ['mix_tags'], ['stats'], ['daily'], ['weekly'], ['monthly'], ['count'], ['locks'], ['delete_locks', 'unlock'], ['reload_conf', 'reload_config'], ['show_conf', 'show_config'], ['pause', 'stopbuy', 'stopentry'], ['whitelist'], ['blacklist'], ['bl_delete', 'blacklist_delete'], ['logs'], ['health'], ['help'], ['version'], ['marketdir'], ['order'], ['list_custom_data'], ['tg_info'], ['profit_long'], ['profit_short']]
2025-08-07 08:22:31,164 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': startup, 'status': "Searching for USDT pairs to buy and sell based on [{'StaticPairList': 'StaticPairList'}]"}
2025-08-07 08:22:31,466 - telegram.ext.Application - INFO - Application started
2025-08-07 08:22:36,191 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-07 08:23:36,191 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-07 08:23:43,374 - uvicorn.error - INFO - 172.19.0.1:41554 - "WebSocket /api/v1/message/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.01kc1l8AJTRJ9jc2bSDWpkta54_NVs4plE6Rqj0iNQg" [accepted]
2025-08-07 08:23:43,377 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(47865cf3, ('172.19.0.1', 41554))
2025-08-07 08:23:43,379 - uvicorn.error - INFO - connection open
2025-08-07 08:23:43,429 - uvicorn.error - INFO - 172.19.0.1:41570 - "WebSocket /api/v1/message/ws?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************.01kc1l8AJTRJ9jc2bSDWpkta54_NVs4plE6Rqj0iNQg" [accepted]
2025-08-07 08:23:43,431 - freqtrade.rpc.api_server.ws.channel - INFO - Connected to channel - WebSocketChannel(ba9d58f7, ('172.19.0.1', 41570))
2025-08-07 08:23:43,433 - uvicorn.error - INFO - connection open
2025-08-07 08:24:36,191 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-07 08:25:01,043 - freqtrade.exchange.exchange_ws - WARNING - ETH/USDT, 5m - Candle date > last refresh (2025-08-07T08:25:00 > 2025-08-07T08:24:59.542). This usually suggests a problem with time synchronization.
2025-08-07 08:25:01,046 - freqtrade.exchange.exchange_ws - WARNING - BTC/USDT, 5m - Candle date > last refresh (2025-08-07T08:25:00 > 2025-08-07T08:24:59.541). This usually suggests a problem with time synchronization.
2025-08-07 08:25:01,048 - freqtrade.exchange.exchange_ws - WARNING - XRP/USDT, 5m - Candle date > last refresh (2025-08-07T08:25:00 > 2025-08-07T08:24:59.843). This usually suggests a problem with time synchronization.
2025-08-07 08:25:41,000 - freqtrade.worker - INFO - Bot heartbeat. PID=1, version='2025.7', state='RUNNING'
2025-08-07 08:25:44,599 - freqtrade.commands.trade_commands - INFO - worker found ... calling exit
2025-08-07 08:25:44,600 - freqtrade.rpc.rpc_manager - INFO - Sending rpc message: {'type': status, 'status': 'process died'}
2025-08-07 08:25:44,602 - freqtrade.freqtradebot - INFO - Cleaning up modules ...
2025-08-07 08:25:44,606 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc modules ...
2025-08-07 08:25:44,608 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.apiserver ...
2025-08-07 08:25:44,610 - freqtrade.rpc.api_server.webserver - INFO - Stopping API Server
2025-08-07 08:25:44,665 - uvicorn.error - INFO - Shutting down
2025-08-07 08:25:44,668 - uvicorn.error - INFO - connection closed
2025-08-07 08:25:44,670 - uvicorn.error - INFO - connection closed
2025-08-07 08:25:44,671 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(47865cf3, ('172.19.0.1', 41554))
2025-08-07 08:25:44,673 - freqtrade.rpc.api_server.ws.channel - INFO - Disconnected from channel - WebSocketChannel(ba9d58f7, ('172.19.0.1', 41570))
2025-08-07 08:25:44,768 - uvicorn.error - INFO - Waiting for application shutdown.
2025-08-07 08:25:44,769 - uvicorn.error - INFO - Application shutdown complete.
2025-08-07 08:25:44,770 - uvicorn.error - INFO - Finished server process [1]
2025-08-07 08:25:44,772 - freqtrade.rpc.rpc_manager - INFO - Cleaning up rpc.telegram ...
2025-08-07 08:25:45,006 - telegram.ext.Application - INFO - Application is stopping. This might take a moment.
2025-08-07 08:25:45,008 - telegram.ext.Application - INFO - Application.stop() complete
2025-08-07 08:25:51,741 - freqtrade.exchange.exchange_ws - INFO - Resetting WS connections.
2025-08-07 08:25:51,743 - freqtrade.exchange.exchange_ws - INFO - BTC/USDT, 5m, spot - Task finished - cancelled
2025-08-07 08:25:51,744 - freqtrade.exchange.exchange_ws - INFO - ETH/USDT, 5m, spot - Task finished - cancelled
2025-08-07 08:25:51,746 - freqtrade.exchange.exchange_ws - INFO - XRP/USDT, 5m, spot - Task finished - cancelled
2025-08-07 08:25:51,749 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-07 08:25:51,756 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-07 08:25:51,758 - freqtrade.exchange.exchange_ws - ERROR - Exception in _unwatch_ohlcv
Traceback (most recent call last):
  File "/freqtrade/freqtrade/exchange/exchange_ws.py", line 144, in _unwatch_ohlcv
    await self._ccxt_object.un_watch_ohlcv_for_symbols([[pair, timeframe]])
  File "/home/<USER>/.local/lib/python3.13/site-packages/ccxt/pro/binance.py", line 1454, in un_watch_ohlcv_for_symbols
    return await self.watch_multiple(url, messageHashes, self.extend(request, params), messageHashes, subscribe)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
ccxt.base.errors.NetworkError: Connection closed by remote server, closing code 1006
2025-08-07 08:25:52,696 - freqtrade - INFO - SIGINT received, aborting ...
2025-08-07 08:25:52,992 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-845' coro=<Exchange.watch_multiple.<locals>.after.<locals>.send_message() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/exchange.py:463> wait_for=<Future pending cb=[Task.task_wakeup()]>>
2025-08-07 08:25:52,995 - asyncio - ERROR - Task was destroyed but it is pending!
task: <Task pending name='Task-846' coro=<Throttler.looper() done, defined at /home/<USER>/.local/lib/python3.13/site-packages/ccxt/async_support/base/throttler.py:21> wait_for=<Future pending cb=[Task.task_wakeup()]>>
