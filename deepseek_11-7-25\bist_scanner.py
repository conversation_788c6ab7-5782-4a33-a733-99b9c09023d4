import pandas as pd
import numpy as np
from tvDatafeed import TvDatafeed, Interval
from datetime import datetime
from scipy.stats import linregress

# ----------------------------
# VERİ YÖNETİM MODÜLÜ
# ----------------------------
class BISTDataFetcher:
    def __init__(self):
        self.tv = TvDatafeed("karamizah", "206557ieCnN")

    def get_bist30_symbols(self):
        return [
            'AKBNK', 'ARCLK', 'ASELS', 'BIMAS', 'DOHOL',
            'EKGYO', 'ENJSA', 'EREGL', 'FROTO', 'GARAN',
            'GUBRF', 'HALKB', 'HEKTS', 'ISCTR', 'ISGYO',
            'KCHOL', 'KOZAA', 'KOZ<PERSON>', 'KRD<PERSON>', 'MGROS',
            'ODAS', 'OY<PERSON><PERSON><PERSON>', 'PETKM', 'PGSUS', 'SAHOL',
            'SAS<PERSON>', 'SISE', 'TAVHL', 'TCELL', 'THYAO',
            'TKFEN', 'TOASO', 'TTKOM', 'TUPRS', 'ULKER',
            'VESTL', 'YKBNK'
        ]

    def fetch_stock_data(self, symbol, n_bars=250):
        try:
            df = self.tv.get_hist(
                symbol=symbol,
                exchange='BIST',
                interval=Interval.in_daily,
                n_bars=n_bars
            )

            # Güncellenmiş sütun isimlendirmesi
            if len(df.columns) == 5:
                df.columns = ['open', 'high', 'low', 'close', 'volume']
            return df
        except Exception as e:
            print(f"tvDatafeed veri çekme hatası ({symbol}): {str(e)}")
            return None

# ----------------------------
# ANALİTİK MOTORU
# ----------------------------
class AdvancedAnalytics:
    @staticmethod
    def calculate_technical_indicators(df):
        """Teknik göstergeleri hesaplar"""
        # Volatilite göstergeleri
        df['MA_20'] = df['close'].rolling(20).mean()
        df['MA_50'] = df['close'].rolling(50).mean()

        # ATR hesaplama (daha doğru versiyon)
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = np.max(ranges, axis=1)
        df['ATR'] = true_range.rolling(14).mean()

        # Momentum göstergeleri
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)

        avg_gain = gain.ewm(alpha=1/14, adjust=False).mean()
        avg_loss = loss.ewm(alpha=1/14, adjust=False).mean()

        rs = avg_gain / avg_loss
        df['RSI'] = 100 - (100 / (1 + rs))

        # Hacim profili
        df['VWAP'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()
        df['Volume_MA'] = df['volume'].rolling(20).mean()

        return df.dropna()

    @staticmethod
    def volatility_breakout_signal(df, threshold=1.5):
        """Volatilite patlaması sinyali"""
        if len(df) < 50: return False

        # Bollinger Bantları
        std = df['close'].rolling(20).std()
        upper_band = df['MA_20'] + 2 * std
        band_width = (upper_band - (df['MA_20'] - 2 * std)) / df['MA_20']

        # Koşullar
        vol_squeeze = band_width.iloc[-1] < band_width.mean() * 0.7
        volume_boom = df['volume'].iloc[-1] > df['Volume_MA'].iloc[-1] * threshold
        breakout = df['close'].iloc[-1] > upper_band.iloc[-1]

        return vol_squeeze and volume_boom and breakout

    @staticmethod
    def smart_money_signal(df):
        """Akıllı para sinyali"""
        if len(df) < 30: return False

        # CMF Hesaplama
        mf_multiplier = ((2 * df['close'] - df['high'] - df['low']) / (df['high'] - df['low']))
        mf_volume = mf_multiplier * df['volume']
        cmf = mf_volume.rolling(20).sum() / df['volume'].rolling(20).sum()

        # Koşullar
        cmf_bullish = cmf.iloc[-1] > 0.05
        vwap_break = df['close'].iloc[-1] > df['VWAP'].iloc[-1] * 1.01
        trend_support = df['close'].iloc[-3:].min() > df['MA_50'].iloc[-3:].min()

        return cmf_bullish and vwap_break and trend_support

    @staticmethod
    def momentum_acceleration_signal(df):
        """Momentum hızlanma sinyali"""
        if len(df) < 50: return False

        # MACD Hesaplama
        exp12 = df['close'].ewm(span=12, adjust=False).mean()
        exp26 = df['close'].ewm(span=26, adjust=False).mean()
        macd = exp12 - exp26
        signal = macd.ewm(span=9, adjust=False).mean()

        # Momentum eğimi
        rsi = df['RSI']
        slope, _, _, _, _ = linregress(range(5), rsi.iloc[-5:])

        # Koşullar
        macd_bullish = macd.iloc[-1] > signal.iloc[-1]
        rsi_trend = slope > 0.5 and rsi.iloc[-1] > 55
        ma_cross = df['MA_20'].iloc[-1] > df['MA_50'].iloc[-1]

        return macd_bullish and rsi_trend and ma_cross

# ----------------------------
# RAPORLAMA VE ÇIKTI
# ----------------------------
class ReportGenerator:
    @staticmethod
    def generate_signals_report(signals, filename="bist_signals_report.html"):
        """HTML formatında sinyal raporu oluşturur"""
        report = """
        <html>
        <head>
            <title>BIST Sinyal Raporu</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2c3e50; }}
                table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                th, td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
                th {{ background-color: #3498db; color: white; }}
                tr:hover {{ background-color: #f5f5f5; }}
                .positive {{ color: #27ae60; font-weight: bold; }}
                .warning {{ color: #f39c12; }}
                .negative {{ color: #c0392b; }}
            </style>
        </head>
        <body>
            <h1>BIST Teknik Sinyal Raporu</h1>
            <p>Oluşturulma tarihi: {date}</p>
            <h2>Tespit Edilen Sinyaller ({count})</h2>
            <table>
                <tr>
                    <th>Hisse</th>
                    <th>Strateji</th>
                    <th>Fiyat (₺)</th>
                    <th>Değişim (%)</th>
                    <th>Hacim (Bin TL)</th>
                    <th>Güçlendirici Faktörler</th>
                </tr>
                {rows}
            </table>
        </body>
        </html>
        """

        rows = ""
        for signal in signals:
            rows += f"""
            <tr>
                <td>{signal['symbol']}</td>
                <td>{signal['strategy']}</td>
                <td>{signal['price']:.2f} ₺</td>
                <td class="{ 'positive' if signal['change'] > 0 else 'negative' }">{signal['change']:.2f}%</td>
                <td>{signal['volume']/1000:.0f}</td>
                <td>{', '.join(signal['strengths'])}</td>
            </tr>
            """

        report = report.format(
            date=datetime.now().strftime("%Y-%m-%d %H:%M"),
            count=len(signals),
            rows=rows
        )

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"Rapor oluşturuldu: {filename}")

# ----------------------------
# ANA İŞLEM
# ----------------------------
def main():
    print("BIST Profesyonel Tarayıcı Başlatılıyor...")

    # 1. Veri Hazırlığı
    fetcher = BISTDataFetcher()
    analytics = AdvancedAnalytics()

    print("BIST-30 hisse listesi alınıyor...")
    symbols = fetcher.get_bist30_symbols()
    print(f"Tarama yapılacak hisse sayısı: {len(symbols)}")

    # 2. Analiz ve Sinyal Tespiti
    all_signals = []

    for symbol in symbols:
        print(f"Analiz ediliyor: {symbol}...")
        try:
            # Veri çekme ve işleme
            df = fetcher.fetch_stock_data(symbol)
            if df is None or len(df) < 100:
                print(f"  >> {symbol} için yeterli veri yok")
                continue

            df = analytics.calculate_technical_indicators(df)

            # Sinyal tespiti
            signals = []

            if analytics.volatility_breakout_signal(df):
                signals.append({
                    'strategy': 'Volatilite Patlaması',
                    'strengths': ['Yüksek Hacim', 'Volatilite Sıkışması']
                })

            if analytics.smart_money_signal(df):
                signals.append({
                    'strategy': 'Akıllı Para Akışı',
                    'strengths': ['CMF Pozitif', 'VWAP Üzeri']
                })

            if analytics.momentum_acceleration_signal(df):
                signals.append({
                    'strategy': 'Momentum Hızlanması',
                    'strengths': ['MACD Pozitif', 'RSI Eğim']
                })

            # Sinyal varsa kaydet
            if signals:
                latest = df.iloc[-1]
                for signal in signals:
                    all_signals.append({
                        'symbol': symbol,
                        'price': latest['close'],
                        'change': (latest['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close'] * 100,
                        'volume': latest['volume'],
                        **signal
                    })
                    print(f"  >> {symbol} için sinyal bulundu: {signal['strategy']}")

        except Exception as e:
            print(f"{symbol} analizinde hata: {str(e)}")

    # 3. Rapor Oluşturma
    print(f"Tespit edilen sinyal sayısı: {len(all_signals)}")
    if all_signals:
        ReportGenerator.generate_signals_report(all_signals)
    else:
        print("Sinyal bulunamadı, rapor oluşturulmadı")

    print("İşlem tamamlandı")

if __name__ == "__main__":
    main()