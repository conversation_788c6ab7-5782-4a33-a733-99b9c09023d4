<?php

declare(strict_types=1);

namespace App\Modules\User\Service;

use App\Core\Validation\Validator;
use App\Modules\User\Models\User;
use App\Modules\User\Repository\UserRepository;
use System\Exception\SystemException;

class UserService {
   // Define validation rules based on User model
   private array $validationRules = [
      'name' => ['required', 'max:100'],
      'middle_name' => ['max:100'],
      'surname' => ['required', 'max:100'],
      'sex' => ['in:male,female,other'], // Assuming these are the valid values
      'email' => ['required', 'email', 'max:100'],
      'phone' => ['required', 'max:20'],
      'password' => ['required', 'min:8'], // Add password rules as needed
      'role' => ['required', 'in:admin,customer,store_owner'] // Example roles
   ];

   // Rules for update (password might not be required)
   private array $updateValidationRules = [
        'name' => ['required', 'max:100'],
        'middle_name' => ['max:100'],
        'surname' => ['required', 'max:100'],
        'sex' => ['in:male,female,other'],
        'email' => ['required', 'email', 'max:100'],
        'phone' => ['required', 'max:20'],
        'password' => ['min:8'], // Password optional on update
        'role' => ['required', 'in:admin,customer,store_owner']
    ];

   public function __construct(
      private UserRepository $repository,
      private Validator $validator // Inject Validator
   ) {
   }

   public function getAll(): array {
      $users = $this->repository->getAll();
      return array_map(function ($user) {
         return $user->jsonSerialize();
      }, $users);
   }

   public function getById(int $userId): array {
      return $this->repository->getById($userId)->jsonSerialize();
   }
   public function update(int $userId, array $data): ?object {
      // Fetch user first to ensure it exists
      $user = $this->repository->getById($userId);
      if (!$user) {
          throw new SystemException('User not found');
      }

      // Validate provided data using update rules
      $updateRules = array_intersect_key($this->updateValidationRules, $data);
      if (!$this->validator->validate($data, $updateRules)) {
          throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Check email/phone uniqueness if they are being updated
      if (isset($data['email'])) {
          $existingUser = $this->repository->findByEmail($data['email']);
          if ($existingUser && $existingUser->getId() !== $userId) {
              throw new SystemException('Email already in use');
          }
      }
      if (isset($data['phone'])) {
          $existingUser = $this->repository->findByPhone($data['phone']);
          if ($existingUser && $existingUser->getId() !== $userId) {
              throw new SystemException('Phone number already in use');
          }
      }

      // Hash password if it's being updated
      if (isset($data['password'])) {
          $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
      }

      return $this->repository->update($userId, $data);
   }
   public function delete(int $userId): bool {
      return $this->repository->delete($userId);
   }
   public function create(array $registerData): User { // Specify return type
      // Add validation using create rules
      if (!$this->validator->validate($registerData, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Check email/phone uniqueness *after* validation
      if ($this->repository->findByEmail($registerData['email'])) {
          throw new SystemException('Email already in use');
      }
      if ($this->repository->findByPhone($registerData['phone'])) {
          throw new SystemException('Phone number already in use');
      }

      // Hash password before creating
      $registerData['password'] = password_hash($registerData['password'], PASSWORD_DEFAULT);

      return $this->repository->create($registerData);
   }
   public function getRoles() {
      return $this->repository->getRoles();
   }
   public function setRole(int $userId, string $role) {
      return $this->repository->setRole($userId, $role);
   }
   public function getRoleById(int $userId) {
      return $this->repository->getRoleById($userId);
   }
   public function checkPermission(int $userId, string $permissionAsking) {
      return $this->repository->checkPermission($userId, $permissionAsking);
   }
   public function requestReset(string $email) {
      return $this->repository->requestReset($email);
   }
   public function resetPassword(string $token, string $newPassword) {
      return $this->repository->resetPassword($token, $newPassword);
   }
}
