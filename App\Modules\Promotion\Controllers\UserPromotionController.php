<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Promotion\Services\UserPromotionService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="User Promotions", description="Kullanıcı Promosyonları Yönetimi")
 */
class UserPromotionController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private UserPromotionService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions",
    *     summary="Tüm Kullanıcı Promosyonlarını Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="<PERSON><PERSON><PERSON><PERSON><PERSON> promosyonları listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      $userPromotions = $this->service->getAllUserPromotions();
      return $this->success($userPromotions);
   }

   /**
    * @OA\Get(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/{id}",
    *     summary="ID ile Kullanıcı Promosyonu Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Kullanıcı Promosyonu ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Kullanıcı promosyonu detayı"),
    *     @OA\Response(response=404, description="Kullanıcı promosyonu bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getById(int $id) {
      $userPromotion = $this->service->getUserPromotionById($id);
      return $this->success($userPromotion);
   }

   /**
    * @OA\Get(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/user/{userId}",
    *     summary="Kullanıcı ID'sine Göre Promosyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="userId",
    *         in="path",
    *         required=true,
    *         description="Kullanıcı ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Kullanıcı promosyonları listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByUserId(int $userId) {
      $userPromotions = $this->service->getUserPromotionsByUserId($userId);
      return $this->success($userPromotions);
   }

   /**
    * @OA\Post(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/assign",
    *     summary="Kullanıcıya Promosyon Ata",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         description="Kullanıcı promosyonu verileri",
    *         @OA\JsonContent(
    *             required={"user_id", "promotion_id"},
    *             @OA\Property(property="user_id", type="integer", example=1),
    *             @OA\Property(property="promotion_id", type="integer", example=1),
    *             @OA\Property(property="used_count", type="integer", example=0)
    *         )
    *     ),
    *     @OA\Response(response=201, description="Kullanıcı promosyonu oluşturuldu"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=409, description="Kullanıcı zaten bu promosyona sahip"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function assign() {
      $data = $this->request->json();
      $userPromotion = $this->service->assignPromotionToUser($data);
      return $this->success($userPromotion);
   }

   /**
    * @OA\Put(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/update/{id}",
    *     summary="Kullanıcı Promosyonunu Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Kullanıcı Promosyonu ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         description="Güncellenecek kullanıcı promosyonu verileri",
    *         @OA\JsonContent(
    *             @OA\Property(property="user_id", type="integer", example=1),
    *             @OA\Property(property="promotion_id", type="integer", example=1),
    *             @OA\Property(property="used_count", type="integer", example=1),
    *             @OA\Property(property="last_used_at", type="string", example="2023-01-01 12:00:00")
    *         )
    *     ),
    *     @OA\Response(response=200, description="Kullanıcı promosyonu güncellendi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Kullanıcı promosyonu bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function update(int $id) {
      $data = $this->request->json();
      $success = $this->service->updateUserPromotion($id, $data);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to update user promotion');
   }

   /**
    * @OA\Delete(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/delete/{id}",
    *     summary="Kullanıcı Promosyonunu Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Kullanıcı Promosyonu ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Kullanıcı promosyonu silindi"),
    *     @OA\Response(response=404, description="Kullanıcı promosyonu bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function delete(int $id) {
      $success = $this->service->deleteUserPromotion($id);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to delete user promotion');
   }

   /**
    * @OA\Post(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/increment-usage",
    *     summary="Kullanıcı Promosyonu Kullanım Sayısını Artır",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         description="Kullanıcı ve promosyon bilgileri",
    *         @OA\JsonContent(
    *             required={"user_id", "promotion_id"},
    *             @OA\Property(property="user_id", type="integer", example=1),
    *             @OA\Property(property="promotion_id", type="integer", example=1)
    *         )
    *     ),
    *     @OA\Response(response=200, description="Kullanım sayısı artırıldı"),
    *     @OA\Response(response=400, description="Geçersiz istek veya kullanım limiti aşıldı"),
    *     @OA\Response(response=404, description="Kullanıcı promosyonu bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function incrementUsage() {
      $data = $this->request->json();

      if (!isset($data['user_id']) || !isset($data['promotion_id'])) {
         return $this->error('User ID and Promotion ID are required');
      }

      $success = $this->service->incrementPromotionUsage($data['user_id'], $data['promotion_id']);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to increment promotion usage');
   }

   /**
    * @OA\Post(
    *     tags={"User Promotions"},
    *     path="/api/user-promotions/validate-code",
    *     summary="Promosyon Kodunu Doğrula",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         description="Promosyon kodu ve kullanıcı bilgileri",
    *         @OA\JsonContent(
    *             required={"code", "user_id"},
    *             @OA\Property(property="code", type="string", example="WELCOME20"),
    *             @OA\Property(property="user_id", type="integer", example=1),
    *             @OA\Property(property="order_amount", type="number", example=100.50)
    *         )
    *     ),
    *     @OA\Response(response=200, description="Promosyon kodu geçerli"),
    *     @OA\Response(response=400, description="Geçersiz promosyon kodu"),
    *     @OA\Response(response=404, description="Promosyon kodu bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function validateCode() {
      $data = $this->request->json();

      if (!isset($data['code']) || !isset($data['user_id'])) {
         return $this->error('Promotion code and User ID are required');
      }

      $orderAmount = $data['order_amount'] ?? null;
      $promotion = $this->service->validatePromotionCode($data['code'], $data['user_id'], $orderAmount);

      return $this->success($promotion);
   }
}
