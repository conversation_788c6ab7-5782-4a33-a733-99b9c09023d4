<template>
   <v-container class="fill-height">
      <v-row justify="center" align="center">
         <v-col cols="12" sm="8" md="6" lg="4">
            <v-card class="elevation-12">
               <v-card-title class="text-center py-4">
                  <h2>{{ t("account.login") }}</h2>
               </v-card-title>
               <v-card-text>
                  <v-form @submit.prevent="loginHandler" ref="form">
                     <v-text-field
                        v-model="username"
                        :label="t('account.username')"
                        prepend-inner-icon="$accountProfile"
                        variant="outlined"
                        class="mb-4"
                        :rules="[requiredRule]"
                        required
                     />
                     <v-text-field
                        v-model="password"
                        :label="t('account.password')"
                        prepend-inner-icon="$lock"
                        variant="outlined"
                        :append-inner-icon="showPassword ? '$eyeOff' : '$eye'"
                        :type="showPassword ? 'text' : 'password'"
                        @click:append-inner="showPassword = !showPassword"
                        :rules="[requiredRule]"
                        required
                     />
                     <v-btn
                        type="submit"
                        color="primary"
                        block
                        class="mt-4"
                        size="large"
                        :loading="loading"
                     >
                        {{ t("account.login") }}
                     </v-btn>
                  </v-form>
               </v-card-text>
               <v-card-actions class="justify-center pb-4">
                  <v-btn
                     variant="text"
                     :to="{ name: 'register' }"
                  >
                     {{ t("account.register") }}
                  </v-btn>
               </v-card-actions>
            </v-card>
         </v-col>
      </v-row>
   </v-container>
</template>

<script lang="ts" setup>
import { useAuthStore } from "@/stores/authStore";
import { useToastStore } from "@/stores/toastStore";
import { appAxios } from "@/utils/axios";
import { appRules } from "@/utils/rules";
import { EToast } from "@/utils/types";
import { ref } from "vue";
import { useI18n } from "vue-i18n";

const { t } = useI18n();
const authStore = useAuthStore();
const toastStore = useToastStore();
const form = ref(null);

const username = ref("");
const password = ref("");
const showPassword = ref(false);
const loading = ref(false);

// Define validation rules
const requiredRule = appRules.required("Bu alan zorunludur");

// Extend toastStore with helper methods
const showSuccessToast = (message: string) => {
  toastStore.add({
    type: EToast.Success,
    message
  });
};

const showErrorToast = (message: string) => {
  toastStore.add({
    type: EToast.Error,
    message
  });
};

const loginHandler = async () => {
   // Form validation is handled by Vuetify rules
   // We'll skip the validate() call since it's not properly typed

   loading.value = true;

   try {
      // API endpoint'i
      const response = await appAxios.post("/api/login", {
         email: username.value,
         password: password.value
      });

      if (response.data) {
         // API'den dönen verileri authStore'a aktarıyoruz
         authStore.userLogin({
            currentUser: response.data.user,
            accessToken: response.data.accessToken,
            refreshToken: response.data.refreshToken
         });

         showSuccessToast(t("account.loginSuccess"));
      }
   } catch (error: any) {
      console.error("Login error:", error);
      showErrorToast(error.response?.data?.message || t("account.loginError"));
   } finally {
      loading.value = false;
   }
};
</script>