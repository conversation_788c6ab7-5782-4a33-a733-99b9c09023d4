<?php

declare(strict_types=1);

namespace App\Modules\Payment\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Payment\Services\PaymentService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Payment", description="Ödeme işlemleri")
 */
class PaymentController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private PaymentService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Payment"},
    *     path="/api/payments",
    *     summary="Tüm Ödemeleri Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Ödeme listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      // Admin veya işletme sahibi rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $payments = $this->service->getAll();
      return $this->success($payments);
   }

   /**
    * @OA\Get(
    *     tags={"Payment"},
    *     path="/api/payments/{id}",
    *     summary="Ödeme Detayını Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Ödeme ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Ödeme detayı"),
    *     @OA\Response(response=404, description="Ödeme bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getById(int $id) {
      // Yetki kontrolü
      if (!$this->hasRole(['admin', 'store_owner', 'customer'])) {
         return $this->forbidden();
      }

      $payment = $this->service->getById($id);

      // Müşteri ise sadece kendi ödemelerini görebilir
      if ($this->hasRole(['customer'])) {
         // Burada ödemenin rezervasyon bilgisini kontrol etmek gerekebilir
         // Şimdilik basit bir kontrol yapıyoruz
         // Gerçek uygulamada rezervasyon servisinden müşteri ID kontrolü yapılmalı
      }

      return $this->success($payment);
   }

   /**
    * @OA\Get(
    *     tags={"Payment"},
    *     path="/api/bookings/{bookingId}/payments",
    *     summary="Rezervasyona Ait Ödemeleri Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="bookingId",
    *         in="path",
    *         required=true,
    *         description="Rezervasyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Ödeme listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByBookingId(int $bookingId) {
      // Yetki kontrolü
      if (!$this->hasRole(['admin', 'store_owner', 'customer'])) {
         return $this->forbidden();
      }

      $payments = $this->service->getByBookingId($bookingId);

      // Müşteri ise sadece kendi rezervasyonlarına ait ödemeleri görebilir
      if ($this->hasRole(['customer'])) {
         // Burada rezervasyon servisinden müşteri ID kontrolü yapılmalı
      }

      return $this->success($payments);
   }

   /**
    * @OA\Post(
    *     tags={"Payment"},
    *     path="/api/payments",
    *     summary="Yeni Ödeme Oluştur",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"booking_id", "payment_method", "amount"},
    *             @OA\Property(property="booking_id", type="integer", example=1),
    *             @OA\Property(property="payment_method", type="string", example="credit_card"),
    *             @OA\Property(property="amount", type="number", format="float", example=100.50),
    *             @OA\Property(property="status", type="string", example="pending")
    *         )
    *     ),
    *     @OA\Response(response=201, description="Ödeme oluşturuldu"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function create() {
      // Yetki kontrolü
      if (!$this->hasRole(['admin', 'store_owner', 'customer'])) {
         return $this->forbidden();
      }

      $data = $this->request->json();
      $payment = $this->service->create($data);
      return $this->success($payment);
   }

   /**
    * @OA\Put(
    *     tags={"Payment"},
    *     path="/api/payments/{id}/status",
    *     summary="Ödeme Durumunu Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Ödeme ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"status"},
    *             @OA\Property(property="status", type="string", example="completed")
    *         )
    *     ),
    *     @OA\Response(response=200, description="Ödeme durumu güncellendi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Ödeme bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function updateStatus(int $id) {
      // Yetki kontrolü - sadece admin ve işletme sahibi
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $data = $this->request->json();

      if (!isset($data['status'])) {
         return $this->error('Status alanı gereklidir');
      }

      $payment = $this->service->updateStatus($id, $data['status']);
      return $this->success($payment);
   }

   /**
    * @OA\Put(
    *     tags={"Payment"},
    *     path="/api/payments/{id}",
    *     summary="Ödeme Bilgilerini Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Ödeme ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             @OA\Property(property="payment_method", type="string", example="bank_transfer"),
    *             @OA\Property(property="amount", type="number", format="float", example=150.75),
    *             @OA\Property(property="status", type="string", example="completed")
    *         )
    *     ),
    *     @OA\Response(response=200, description="Ödeme güncellendi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Ödeme bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function update(int $id) {
      // Yetki kontrolü - sadece admin ve işletme sahibi
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $data = $this->request->json();
      $payment = $this->service->update($id, $data);
      return $this->success($payment);
   }

   /**
    * @OA\Delete(
    *     tags={"Payment"},
    *     path="/api/payments/{id}",
    *     summary="Ödeme Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Ödeme ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Ödeme silindi"),
    *     @OA\Response(response=404, description="Ödeme bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function delete(int $id) {
      // Yetki kontrolü - sadece admin
      if (!$this->hasRole(['admin'])) {
         return $this->forbidden();
      }

      $this->service->delete($id);
      return $this->success();
   }

   /**
    * @OA\Get(
    *     tags={"Payment"},
    *     path="/api/payments/status/{status}",
    *     summary="Duruma Göre Ödemeleri Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="status",
    *         in="path",
    *         required=true,
    *         description="Ödeme Durumu",
    *         @OA\Schema(type="string", enum={"pending", "completed", "failed"})
    *     ),
    *     @OA\Response(response=200, description="Ödeme listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByStatus(string $status) {
      // Yetki kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $payments = $this->service->getByStatus($status);
      return $this->success($payments);
   }

   /**
    * @OA\Get(
    *     tags={"Payment"},
    *     path="/api/payments/method/{method}",
    *     summary="Ödeme Yöntemine Göre Ödemeleri Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="method",
    *         in="path",
    *         required=true,
    *         description="Ödeme Yöntemi",
    *         @OA\Schema(type="string", enum={"credit_card", "cash", "bank_transfer"})
    *     ),
    *     @OA\Response(response=200, description="Ödeme listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByPaymentMethod(string $method) {
      // Yetki kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $payments = $this->service->getByPaymentMethod($method);
      return $this->success($payments);
   }

   /**
    * @OA\Get(
    *     tags={"Payment"},
    *     path="/api/payments/date-range",
    *     summary="Tarih Aralığına Göre Ödemeleri Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=true,
    *         description="Başlangıç Tarihi (YYYY-MM-DD)",
    *         @OA\Schema(type="string", format="date")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=true,
    *         description="Bitiş Tarihi (YYYY-MM-DD)",
    *         @OA\Schema(type="string", format="date")
    *     ),
    *     @OA\Response(response=200, description="Ödeme listesi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByDateRange() {
      // Yetki kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }
      $startDate = $this->request->get('start_date');
      $endDate = $this->request->get('end_date');

      if (!$startDate || !$endDate) {
         return $this->error('Başlangıç ve bitiş tarihleri gereklidir');
      }

      $payments = $this->service->getByDateRange($startDate, $endDate);
      return $this->success($payments);
   }
}
