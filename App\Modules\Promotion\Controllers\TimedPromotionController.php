<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Promotion\Services\TimedPromotionService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Timed Promotions", description="Zamanlı Promosyonlar Yönetimi")
 */
class TimedPromotionController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private TimedPromotionService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Timed Promotions"},
    *     path="/api/timed-promotions",
    *     summary="Tüm Zamanlı Promosyonları Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Zamanlı promosyonlar listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      $timedPromotions = $this->service->getAllTimedPromotions();
      return $this->success($timedPromotions);
   }

   /**
    * @OA\Get(
    *     tags={"Timed Promotions"},
    *     path="/api/timed-promotions/{id}",
    *     summary="ID ile Zamanlı Promosyon Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Zamanlı Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Zamanlı promosyon detayı"),
    *     @OA\Response(response=404, description="Zamanlı promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getById(int $id) {
      $timedPromotion = $this->service->getTimedPromotionById($id);
      return $this->success($timedPromotion);
   }

   /**
    * @OA\Get(
    *     tags={"Timed Promotions"},
    *     path="/api/timed-promotions/promotion/{promotionId}",
    *     summary="Promosyon ID'sine Göre Zamanlı Promosyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="promotionId",
    *         in="path",
    *         required=true,
    *         description="Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Zamanlı promosyonlar listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByPromotionId(int $promotionId) {
      $timedPromotions = $this->service->getTimedPromotionsByPromotionId($promotionId);
      return $this->success($timedPromotions);
   }

   /**
    * @OA\Get(
    *     tags={"Timed Promotions"},
    *     path="/api/timed-promotions/active",
    *     summary="Şu Anda Aktif Olan Zamanlı Promosyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Aktif zamanlı promosyonlar listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getActive() {
      $timedPromotions = $this->service->getCurrentlyActiveTimedPromotions();
      return $this->success($timedPromotions);
   }

   /**
    * @OA\Post(
    *     tags={"Timed Promotions"},
    *     path="/timed-promotions/create",
    *     summary="Yeni Zamanlı Promosyon Oluştur",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         description="Zamanlı promosyon verileri",
    *         @OA\JsonContent(
    *             required={"promotion_id", "applicable_days", "start_time", "end_time"},
    *             @OA\Property(property="promotion_id", type="integer", example=1),
    *             @OA\Property(property="applicable_days", type="string", example="monday", enum={"monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"}),
    *             @OA\Property(property="start_time", type="string", example="09:00:00"),
    *             @OA\Property(property="end_time", type="string", example="17:00:00")
    *         )
    *     ),
    *     @OA\Response(response=201, description="Zamanlı promosyon oluşturuldu"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function create() {
      $data = $this->request->json();
      $timedPromotion = $this->service->createTimedPromotion($data);
      return $this->success($timedPromotion);
   }

   /**
    * @OA\Put(
    *     tags={"Timed Promotions"},
    *     path="/timed-promotions/update/{id}",
    *     summary="Zamanlı Promosyonu Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Zamanlı Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         description="Güncellenecek zamanlı promosyon verileri",
    *         @OA\JsonContent(
    *             @OA\Property(property="promotion_id", type="integer", example=1),
    *             @OA\Property(property="applicable_days", type="string", example="monday", enum={"monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"}),
    *             @OA\Property(property="start_time", type="string", example="10:00:00"),
    *             @OA\Property(property="end_time", type="string", example="18:00:00")
    *         )
    *     ),
    *     @OA\Response(response=200, description="Zamanlı promosyon güncellendi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Zamanlı promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function update(int $id) {
      $data = $this->request->json();
      $success = $this->service->updateTimedPromotion($id, $data);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to update timed promotion');
   }

   /**
    * @OA\Delete(
    *     tags={"Timed Promotions"},
    *     path="/api/timed-promotions/delete/{id}",
    *     summary="Zamanlı Promosyonu Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Zamanlı Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Zamanlı promosyon silindi"),
    *     @OA\Response(response=404, description="Zamanlı promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function delete(int $id) {
      $success = $this->service->deleteTimedPromotion($id);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to delete timed promotion');
   }
}
