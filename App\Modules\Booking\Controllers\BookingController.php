<?php

declare(strict_types=1);

namespace App\Modules\Booking\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Booking\Services\BookingService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Booking", description="Rezervasyon işlemleri")
 */
class BookingController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private BookingService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings",
    *     summary="Tüm Rezervasyonları Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Rezervasyon listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      // Admin veya işletme sahibi rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $bookings = $this->service->getAll();
      return $this->success($bookings);
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings/{id}",
    *     summary="Rezervasyon Detayı Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Rezervasyon detayı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=404, description="Booking not found"),
    * )
    */
   public function getById(int $id) {
      $booking = $this->service->getById($id);
      return $this->success($booking);
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings/customer",
    *     summary="Müşteriye Ait Rezervasyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Müşteriye ait rezervasyonlar"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getCustomerBookings() {
      $userId = $this->auth->getUser()['id'];
      if (!$userId) {
         return $this->forbidden();
      }

      $bookings = $this->service->getCustomerBookings($userId);
      return $this->success($bookings);
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings/store/{storeId}",
    *     summary="İşletmeye Ait Rezervasyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="storeId",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="İşletmeye ait rezervasyonlar"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=403, description="Forbidden"),
    * )
    */
   public function getStoreBookings(int $storeId) {
      // İşletme sahibi veya admin rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $bookings = $this->service->getStoreBookings($storeId);
      return $this->success($bookings);
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings/employee/{employeeId}",
    *     summary="Çalışana Ait Rezervasyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="employeeId",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Çalışana ait rezervasyonlar"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=403, description="Forbidden"),
    * )
    */
   public function getEmployeeBookings(int $employeeId) {
      // İşletme sahibi, çalışan veya admin rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner', 'employee'])) {
         return $this->forbidden();
      }

      $bookings = $this->service->getEmployeeBookings($employeeId);
      return $this->success($bookings);
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings/status/{status}",
    *     summary="Durum Bazlı Rezervasyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="status",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string", enum={"pending", "confirmed", "cancelled", "completed"})
    *     ),
    *     @OA\Response(response=200, description="Durum bazlı rezervasyonlar"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=403, description="Forbidden"),
    * )
    */
   public function getBookingsByStatus(string $status) {
      // İşletme sahibi veya admin rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $bookings = $this->service->getBookingsByStatus($status);
      return $this->success($bookings);
   }

   /**
    * @OA\Post(
    *     tags={"Booking"},
    *     path="/api/bookings/create",
    *     summary="Yeni Rezervasyon Oluştur",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"customer_id", "store_id", "service_id", "employee_id", "start_time", "end_time", "final_price"},
    *             @OA\Property(property="customer_id", type="integer"),
    *             @OA\Property(property="store_id", type="integer"),
    *             @OA\Property(property="service_id", type="integer"),
    *             @OA\Property(property="employee_id", type="integer"),
    *             @OA\Property(property="start_time", type="string", format="date-time"),
    *             @OA\Property(property="end_time", type="string", format="date-time"),
    *             @OA\Property(property="final_price", type="number", format="float"),
    *             @OA\Property(property="status", type="string", enum={"pending", "confirmed", "cancelled", "completed"}, default="pending")
    *         )
    *     ),
    *     @OA\Response(response=201, description="Rezervasyon oluşturuldu"),
    *     @OA\Response(response=400, description="Bad request"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=409, description="Conflicting booking exists"),
    * )
    */
   public function create() {
      $userId = $this->auth->getUser()['id'];
      if (!$userId) {
         return $this->forbidden();
      }

      $data = $this->request->json();

      // Validasyon
      $requiredFields = ['store_id', 'service_id', 'employee_id', 'start_time', 'end_time', 'final_price'];
      foreach ($requiredFields as $field) {
         if (!isset($data[$field])) {
            return $this->notFound("Missing required field: {$field}");
         }
      }

      // Müşteri ID'si olarak giriş yapan kullanıcının ID'sini kullan
      $data['customer_id'] = $userId;

      try {
         $booking = $this->service->createBooking($data);
         return $this->success($booking->jsonSerialize());
      } catch (\Exception $e) {
         return $this->jsonResponse($e->getCode() ?: 400, $e->getMessage());
      }
   }

   /**
    * @OA\Put(
    *     tags={"Booking"},
    *     path="/api/bookings/update/{id}",
    *     summary="Rezervasyon Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             @OA\Property(property="customer_id", type="integer"),
    *             @OA\Property(property="store_id", type="integer"),
    *             @OA\Property(property="service_id", type="integer"),
    *             @OA\Property(property="employee_id", type="integer"),
    *             @OA\Property(property="start_time", type="string", format="date-time"),
    *             @OA\Property(property="end_time", type="string", format="date-time"),
    *             @OA\Property(property="final_price", type="number", format="float"),
    *             @OA\Property(property="status", type="string", enum={"pending", "confirmed", "cancelled", "completed"})
    *         )
    *     ),
    *     @OA\Response(response=200, description="Rezervasyon güncellendi"),
    *     @OA\Response(response=400, description="Bad request"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=403, description="Forbidden"),
    *     @OA\Response(response=404, description="Booking not found"),
    *     @OA\Response(response=409, description="Conflicting booking exists"),
    * )
    */
   public function update(int $id) {
      // Admin veya işletme sahibi rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $data = $this->request->json();

      try {
         $this->service->updateBooking($id, $data);
         return $this->success();
      } catch (\Exception $e) {
         return $this->jsonResponse($e->getCode() ?: 400, $e->getMessage());
      }
   }

   /**
    * @OA\Put(
    *     tags={"Booking"},
    *     path="/api/bookings/status/{id}",
    *     summary="Rezervasyon Durumunu Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"status"},
    *             @OA\Property(property="status", type="string", enum={"pending", "confirmed", "cancelled", "completed"})
    *         )
    *     ),
    *     @OA\Response(response=200, description="Rezervasyon durumu güncellendi"),
    *     @OA\Response(response=400, description="Bad request"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=403, description="Forbidden"),
    *     @OA\Response(response=404, description="Booking not found"),
    * )
    */
   public function updateStatus(int $id) {
      // Admin veya işletme sahibi rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $data = $this->request->json();

      if (!isset($data['status'])) {
         return $this->notFound("Missing required field: status");
      }

      $this->service->updateBookingStatus($id, $data['status']);
      return $this->success();
   }

   /**
    * @OA\Delete(
    *     tags={"Booking"},
    *     path="/api/bookings/delete/{id}",
    *     summary="Rezervasyon Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Rezervasyon silindi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=403, description="Forbidden"),
    *     @OA\Response(response=404, description="Booking not found"),
    * )
    */
   public function delete(int $id) {
      // Admin veya işletme sahibi rolü kontrolü
      if (!$this->hasRole(['admin', 'store_owner'])) {
         return $this->forbidden();
      }

      $this->service->deleteBooking($id);
      return $this->success();
   }

   /**
    * @OA\Get(
    *     tags={"Booking"},
    *     path="/api/bookings/availability",
    *     summary="Müsaitlik Kontrolü",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="employee_id",
    *         in="query",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Parameter(
    *         name="start_time",
    *         in="query",
    *         required=true,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_time",
    *         in="query",
    *         required=true,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Response(response=200, description="Müsaitlik durumu"),
    *     @OA\Response(response=400, description="Bad request"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function checkAvailability() {
      $params = $this->request->json();

      // Validasyon
      $requiredParams = ['employee_id', 'start_time', 'end_time'];
      foreach ($requiredParams as $param) {
         if (!isset($params[$param])) {
            return $this->notFound("Missing required parameter: {$param}");
         }
      }

      $isAvailable = $this->service->checkAvailability(
         (int)$params['employee_id'],
         $params['start_time'],
         $params['end_time']
      );

      return $this->success([
         'is_available' => $isAvailable
      ]);
   }
}
