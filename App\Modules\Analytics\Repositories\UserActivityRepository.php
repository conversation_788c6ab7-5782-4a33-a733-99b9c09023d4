<?php

declare(strict_types=1);

namespace App\Modules\Analytics\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Analytics\Models\UserActivity;
use System\Database\Database;
use System\Exception\ExceptionHandler;
use System\Exception\SystemException;

class UserActivityRepository extends BaseRepository {
   protected string $table = 'user_activity';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu UserActivity nesnesine dönüştürür.
    *
    * @param array $data
    * @return UserActivity
    */
   protected function mapToObject(array $data): UserActivity {
      return new UserActivity(...$data);
   }

   /**
    * Kullanıcı aktivitesini kaydeder veya günceller.
    *
    * @param array $data
    * @return UserActivity
    */
   public function trackActivity(array $data): UserActivity {
      try {
         // Önce aynı kullanıcı ve aktivite tipi için kayıt var mı kontrol et
         $existingActivity = $this->findUserActivity($data['user_id'], $data['activity_type']);

         if ($existingActivity) {
            // Mevcut aktiviteyi güncelle
            $stmt = $this->database->prepare(
               "UPDATE {$this->table}
               SET activity_count = activity_count + 1,
                   last_activity_at = NOW(),
                   metadata = :metadata,
                   updated_at = NOW()
               WHERE id = :id"
            );

            $metadata = isset($data['metadata']) ? json_encode($data['metadata']) : $existingActivity->getMetadata();

            $stmt->execute([
               'metadata' => $metadata,
               'id' => $existingActivity->getId()
            ]);

            return $this->getById($existingActivity->getId());
         } else {
            // Yeni aktivite oluştur
            $stmt = $this->database->prepare(
               "INSERT INTO {$this->table}
               (user_id, activity_type, activity_count, last_activity_at, first_activity_at, metadata)
               VALUES (:user_id, :activity_type, :activity_count, NOW(), NOW(), :metadata)"
            );

            $params = [
               'user_id' => $data['user_id'],
               'activity_type' => $data['activity_type'],
               'activity_count' => $data['activity_count'] ?? 1,
               'metadata' => isset($data['metadata']) ? json_encode($data['metadata']) : null
            ];

            $stmt->execute($params);
            $id = (int) $this->database->getLastId();

            return $this->getById($id);
         }
      } catch (\Exception $e) {
         throw new SystemException('Failed to track user activity: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir kullanıcı ve aktivite tipi için aktivite arar.
    *
    * @param int $userId
    * @param string $activityType
    * @return UserActivity|null
    */
   public function findUserActivity(int $userId, string $activityType): ?UserActivity {
      $sql = "SELECT * FROM {$this->table} WHERE user_id = :user_id AND activity_type = :activity_type";
      $params = [
         'user_id' => $userId,
         'activity_type' => $activityType
      ];

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $result = $stmt->getRow();

      if (!$result) {
         return null;
      }

      return $this->mapToObject((array) $result);
   }

   /**
    * Belirli bir kullanıcının tüm aktivitelerini getirir.
    *
    * @param int $userId
    * @return array
    */
   public function getUserActivities(int $userId): array {
      $sql = "SELECT * FROM {$this->table} WHERE user_id = :user_id ORDER BY last_activity_at DESC";
      $params = ['user_id' => $userId];

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $activities = $stmt->getAll();

      return array_map(function ($activity) {
         return $this->mapToObject((array) $activity);
      }, $activities);
   }

   /**
    * Belirli bir aktivite tipine göre en aktif kullanıcıları getirir.
    *
    * @param string $activityType
    * @param int $limit
    * @return array
    */
   public function getMostActiveUsers(string $activityType, int $limit = 10): array {
      $sql = "SELECT * FROM {$this->table}
              WHERE activity_type = :activity_type
              ORDER BY activity_count DESC
              LIMIT :limit";

      $params = [
         'activity_type' => $activityType,
         'limit' => $limit
      ];

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $activities = $stmt->getAll();

      return array_map(function ($activity) {
         return $this->mapToObject((array) $activity);
      }, $activities);
   }

   /**
    * Son aktivite zamanına göre en son aktif olan kullanıcıları getirir.
    *
    * @param int $limit
    * @return array
    */
   public function getRecentlyActiveUsers(int $limit = 10): array {
      $sql = "SELECT DISTINCT user_id, MAX(last_activity_at) as last_activity
              FROM {$this->table}
              GROUP BY user_id
              ORDER BY last_activity DESC
              LIMIT :limit";

      $stmt = $this->database->prepare($sql);
      $stmt->execute(['limit' => $limit]);
      $results = $stmt->getAll();

      $activities = [];
      foreach ($results as $result) {
         $sql = "SELECT * FROM {$this->table}
                 WHERE user_id = :user_id
                 ORDER BY last_activity_at DESC
                 LIMIT 1";

         $stmt = $this->database->prepare($sql);
         $stmt->execute(['user_id' => $result->user_id]);
         $activity = $stmt->getRow();

         if ($activity) {
            $activities[] = $this->mapToObject((array) $activity);
         }
      }

      return $activities;
   }

   /**
    * Belirli bir zaman aralığında aktif olan kullanıcıları getirir.
    *
    * @param string $startDate
    * @param string $endDate
    * @param string|null $activityType
    * @return array
    */
   public function getActiveUsersInPeriod(string $startDate, string $endDate, ?string $activityType = null): array {
      $sql = "SELECT * FROM {$this->table}
              WHERE last_activity_at BETWEEN :start_date AND :end_date";

      $params = [
         'start_date' => $startDate,
         'end_date' => $endDate
      ];

      if ($activityType !== null) {
         $sql .= " AND activity_type = :activity_type";
         $params['activity_type'] = $activityType;
      }

      $sql .= " ORDER BY last_activity_at DESC";

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $activities = $stmt->getAll();

      return array_map(function ($activity) {
         return $this->mapToObject((array) $activity);
      }, $activities);
   }

   /**
    * Belirli bir zaman aralığında aktif olan benzersiz kullanıcı sayısını getirir.
    *
    * @param string $startDate
    * @param string $endDate
    * @param string|null $activityType
    * @return int
    */
   public function countActiveUsersInPeriod(string $startDate, string $endDate, ?string $activityType = null): int {
      $sql = "SELECT COUNT(DISTINCT user_id) as count
              FROM {$this->table}
              WHERE last_activity_at BETWEEN :start_date AND :end_date";

      $params = [
         'start_date' => $startDate,
         'end_date' => $endDate
      ];

      if ($activityType !== null) {
         $sql .= " AND activity_type = :activity_type";
         $params['activity_type'] = $activityType;
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $result = $stmt->getRow();

      return (int) ($result->count ?? 0);
   }
}
