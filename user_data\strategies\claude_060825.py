# pragma pylint: disable=missing-docstring, invalid-name, pointless-string-statement
# flake8: noqa: F401
# isort: skip_file

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from pandas import DataFrame
from typing import Optional, Union
from scipy import stats

from freqtrade.strategy import (
    IStrategy,
    Trade,
    Order,
    PairLocks,
    informative,  # @informative decorator
    # Hyperopt Parameters
    BooleanParameter,
    CategoricalParameter,
    DecimalParameter,
    IntParameter,
    RealParameter,
    # timeframe helpers
    timeframe_to_minutes,
    timeframe_to_next_date,
    timeframe_to_prev_date,
    # Strategy helper functions
    merge_informative_pair,
    stoploss_from_absolute,
    stoploss_from_open,
)

import talib.abstract as ta
from technical import qtpylib


class AdvancedAlphaStrategy(IStrategy):
    """
    Advanced Multi-Factor Alpha Strategy

    Features:
    - Multi-timeframe analysis with informative pairs
    - Dynamic volatility-based position sizing
    - Adaptive risk management with trailing stops
    - Machine learning-inspired momentum persistence scoring
    - Market regime detection (trending vs ranging)
    - Volume profile analysis
    - Correlation-based pair selection
    - Advanced technical indicators (SuperTrend, VWAP, etc.)
    - Risk-adjusted returns optimization
    """

    INTERFACE_VERSION = 3
    can_short: bool = True

    # Dynamic ROI based on market conditions
    minimal_roi = {
        "0": 0.08,    # 8% initial target
        "30": 0.05,   # 5% after 30 min
        "60": 0.03,   # 3% after 1 hour
        "120": 0.02,  # 2% after 2 hours
        "240": 0.01,  # 1% after 4 hours
        "480": 0.005, # 0.5% after 8 hours
    }

    # Adaptive stoploss
    stoploss = -0.08
    trailing_stop = True
    trailing_only_offset_is_reached = True
    trailing_stop_positive = 0.02
    trailing_stop_positive_offset = 0.04

    timeframe = '5m'
    informative_timeframe = '1h'

    process_only_new_candles = True
    use_exit_signal = True
    exit_profit_only = False
    ignore_roi_if_entry_signal = False

    startup_candle_count: int = 400

    # Hyperoptable Parameters
    # Momentum Parameters
    rsi_period = IntParameter(12, 21, default=14, space="buy", optimize=True)
    rsi_buy_threshold = IntParameter(25, 40, default=32, space="buy", optimize=True)
    rsi_sell_threshold = IntParameter(60, 80, default=68, space="sell", optimize=True)

    # Volatility Parameters
    atr_period = IntParameter(10, 30, default=14, space="buy", optimize=True)
    volatility_threshold = DecimalParameter(0.01, 0.05, default=0.025, space="buy", optimize=True)

    # Trend Parameters
    supertrend_period = IntParameter(8, 15, default=10, space="buy", optimize=True)
    supertrend_multiplier = DecimalParameter(2.0, 4.0, default=3.0, space="buy", optimize=True)

    # Volume Parameters
    volume_sma_period = IntParameter(10, 30, default=20, space="buy", optimize=True)
    volume_threshold = DecimalParameter(1.2, 2.5, default=1.8, space="buy", optimize=True)

    # Machine Learning Features
    momentum_persistence_period = IntParameter(5, 20, default=10, space="buy", optimize=True)
    trend_strength_threshold = DecimalParameter(0.3, 0.8, default=0.5, space="buy", optimize=True)

    # Risk Management
    max_drawdown_threshold = DecimalParameter(0.03, 0.08, default=0.05, space="protection", optimize=True)
    correlation_threshold = DecimalParameter(0.5, 0.9, default=0.7, space="protection", optimize=True)

    order_types = {
        'entry': 'limit',
        'exit': 'limit',
        'stoploss': 'market',
        'stoploss_on_exchange': False,
    }

    order_time_in_force = {
        'entry': 'GTC',
        'exit': 'GTC'
    }

    plot_config = {
        'main_plot': {
            'supertrend_upper': {'color': 'red'},
            'supertrend_lower': {'color': 'green'},
            'vwap': {'color': 'blue'},
            'ema_fast': {'color': 'orange'},
            'ema_slow': {'color': 'purple'},
        },
        'subplots': {
            "Alpha Score": {
                'alpha_score': {'color': 'cyan'},
                'trend_strength': {'color': 'yellow'},
            },
            "Volume Analysis": {
                'volume_ratio': {'color': 'brown'},
                'volume_sma': {'color': 'pink'},
            },
            "Risk Metrics": {
                'volatility_rank': {'color': 'gray'},
                'momentum_persistence': {'color': 'lime'},
            }
        }
    }

    @informative('1h')
    def populate_indicators_1h(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Higher timeframe indicators for trend confirmation"""

        # Trend indicators
        dataframe['ema_21_1h'] = ta.EMA(dataframe, timeperiod=21)
        dataframe['ema_55_1h'] = ta.EMA(dataframe, timeperiod=55)
        dataframe['adx_1h'] = ta.ADX(dataframe, timeperiod=14)

        # Volume indicators
        dataframe['volume_sma_1h'] = ta.SMA(dataframe['volume'], timeperiod=20)
        dataframe['vwap_1h'] = qtpylib.vwap(dataframe)

        # Volatility
        dataframe['atr_1h'] = ta.ATR(dataframe, timeperiod=14)
        dataframe['bb_upper_1h'], dataframe['bb_mid_1h'], dataframe['bb_lower_1h'] = ta.BBANDS(dataframe)

        # Market regime detection
        dataframe['regime_1h'] = np.where(
            (dataframe['ema_21_1h'] > dataframe['ema_55_1h']) &
            (dataframe['adx_1h'] > 25), 1,  # Trending up
            np.where(
                (dataframe['ema_21_1h'] < dataframe['ema_55_1h']) &
                (dataframe['adx_1h'] > 25), -1,  # Trending down
                0  # Ranging
            )
        )

        return dataframe

    def populate_indicators(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Main timeframe indicators"""

        # Basic Price Action
        dataframe['hl2'] = (dataframe['high'] + dataframe['low']) / 2
        dataframe['hlc3'] = (dataframe['high'] + dataframe['low'] + dataframe['close']) / 3

        # Moving Averages
        dataframe['ema_fast'] = ta.EMA(dataframe, timeperiod=8)
        dataframe['ema_slow'] = ta.EMA(dataframe, timeperiod=21)
        dataframe['sma_200'] = ta.SMA(dataframe, timeperiod=200)

        # VWAP
        dataframe['vwap'] = qtpylib.vwap(dataframe)

        # RSI with multiple timeframes
        dataframe['rsi'] = ta.RSI(dataframe, timeperiod=self.rsi_period.value)
        dataframe['rsi_sma'] = ta.SMA(dataframe['rsi'], timeperiod=5)

        # SuperTrend
        hl2 = (dataframe['high'] + dataframe['low']) / 2
        atr = ta.ATR(dataframe, timeperiod=self.supertrend_period.value)

        upper_band = hl2 + (self.supertrend_multiplier.value * atr)
        lower_band = hl2 - (self.supertrend_multiplier.value * atr)

        dataframe['supertrend_upper'] = upper_band
        dataframe['supertrend_lower'] = lower_band

        # SuperTrend calculation
        supertrend = np.where(
            dataframe['close'] > upper_band.shift(1), lower_band,
            np.where(dataframe['close'] < lower_band.shift(1), upper_band, np.nan)
        )
        dataframe['supertrend'] = pd.Series(supertrend).ffill()

        # Volatility Analysis
        dataframe['atr'] = ta.ATR(dataframe, timeperiod=self.atr_period.value)
        dataframe['atr_percent'] = (dataframe['atr'] / dataframe['close']) * 100
        dataframe['volatility_rank'] = dataframe['atr_percent'].rolling(50).rank() / 50

        # Bollinger Bands
        bb_upper, bb_mid, bb_lower = ta.BBANDS(dataframe, timeperiod=20, nbdevup=2, nbdevdn=2)
        dataframe['bb_upper'] = bb_upper
        dataframe['bb_mid'] = bb_mid
        dataframe['bb_lower'] = bb_lower
        dataframe['bb_percent'] = (dataframe['close'] - bb_lower) / (bb_upper - bb_lower)
        dataframe['bb_width'] = (bb_upper - bb_lower) / bb_mid

        # Volume Analysis
        dataframe['volume_sma'] = ta.SMA(dataframe['volume'], timeperiod=self.volume_sma_period.value)
        dataframe['volume_ratio'] = dataframe['volume'] / dataframe['volume_sma']

        # Price-Volume Trend
        dataframe['pvt'] = ta.AD(dataframe)  # Accumulation/Distribution
        dataframe['obv'] = ta.OBV(dataframe)

        # MACD
        macd_line, macd_signal, macd_hist = ta.MACD(dataframe)
        dataframe['macd'] = macd_line
        dataframe['macd_signal'] = macd_signal
        dataframe['macd_hist'] = macd_hist

        # Stochastic
        slowk, slowd = ta.STOCH(dataframe)
        dataframe['stoch_k'] = slowk
        dataframe['stoch_d'] = slowd

        # Williams %R
        dataframe['williams_r'] = ta.WILLR(dataframe)

        # Money Flow Index
        dataframe['mfi'] = ta.MFI(dataframe)

        # Advanced Features

        # Momentum Persistence Score (ML-inspired)
        returns = dataframe['close'].pct_change()
        dataframe['momentum_persistence'] = returns.rolling(
            self.momentum_persistence_period.value
        ).apply(lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0.5)

        # Trend Strength (based on consecutive closes above/below MA)
        dataframe['trend_strength'] = np.where(
            dataframe['close'] > dataframe['ema_slow'],
            (dataframe['close'] > dataframe['ema_slow']).rolling(10).sum() / 10,
            -(dataframe['close'] < dataframe['ema_slow']).rolling(10).sum() / 10
        )

        # Price Action Patterns
        dataframe['higher_high'] = (
            (dataframe['high'] > dataframe['high'].shift(1)) &
            (dataframe['high'].shift(1) > dataframe['high'].shift(2))
        ).astype(int)

        dataframe['lower_low'] = (
            (dataframe['low'] < dataframe['low'].shift(1)) &
            (dataframe['low'].shift(1) < dataframe['low'].shift(2))
        ).astype(int)

        # Composite Alpha Score
        dataframe['alpha_score'] = (
            # Momentum component
            ((dataframe['rsi'] - 50) / 50) * 0.25 +
            # Trend component
            (dataframe['trend_strength']) * 0.3 +
            # Volume component
            (np.log(dataframe['volume_ratio']) / 2) * 0.2 +
            # Volatility component (inverse relationship)
            ((1 - dataframe['volatility_rank']) - 0.5) * 0.15 +
            # Momentum persistence
            ((dataframe['momentum_persistence'] - 0.5) * 2) * 0.1
        )

        # Risk Metrics
        dataframe['sharpe_proxy'] = (
            dataframe['close'].pct_change().rolling(20).mean() /
            dataframe['close'].pct_change().rolling(20).std()
        )

        return dataframe

    def populate_entry_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Advanced entry logic with multiple confirmations"""

        # Long Entry Conditions
        long_conditions = [
            # Primary trend confirmation
            dataframe['close'] > dataframe['supertrend'],
            dataframe['ema_fast'] > dataframe['ema_slow'],

            # Momentum confirmation
            dataframe['rsi'] > self.rsi_buy_threshold.value,
            dataframe['rsi'] < 50,  # Not overbought
            dataframe['macd'] > dataframe['macd_signal'],

            # Volume confirmation
            dataframe['volume_ratio'] > self.volume_threshold.value,
            dataframe['obv'] > dataframe['obv'].shift(1),

            # Alpha score confirmation
            dataframe['alpha_score'] > 0.2,
            dataframe['momentum_persistence'] > 0.6,

            # Risk management
            dataframe['volatility_rank'] < 0.8,  # Not in highest volatility regime
            dataframe['bb_percent'] > 0.2,  # Not at bottom of BB
            dataframe['bb_percent'] < 0.8,  # Not at top of BB
        ]

        # Higher timeframe confirmation
        if len(dataframe) > 0:
            long_conditions.extend([
                dataframe['ema_21_1h'] > dataframe['ema_55_1h'],  # HTF uptrend
                dataframe['regime_1h'] >= 0,  # Not in downtrend regime
            ])

        dataframe.loc[
            reduce(lambda x, y: x & y, long_conditions),
            'enter_long'
        ] = 1

        # Short Entry Conditions
        short_conditions = [
            # Primary trend confirmation
            dataframe['close'] < dataframe['supertrend'],
            dataframe['ema_fast'] < dataframe['ema_slow'],

            # Momentum confirmation
            dataframe['rsi'] < (100 - self.rsi_buy_threshold.value),  # Oversold for shorts
            dataframe['rsi'] > 50,  # Not oversold
            dataframe['macd'] < dataframe['macd_signal'],

            # Volume confirmation
            dataframe['volume_ratio'] > self.volume_threshold.value,
            dataframe['obv'] < dataframe['obv'].shift(1),

            # Alpha score confirmation
            dataframe['alpha_score'] < -0.2,
            dataframe['momentum_persistence'] < 0.4,

            # Risk management
            dataframe['volatility_rank'] < 0.8,  # Not in highest volatility regime
            dataframe['bb_percent'] > 0.2,  # Not at bottom of BB
            dataframe['bb_percent'] < 0.8,  # Not at top of BB
        ]

        # Higher timeframe confirmation
        if len(dataframe) > 0:
            short_conditions.extend([
                dataframe['ema_21_1h'] < dataframe['ema_55_1h'],  # HTF downtrend
                dataframe['regime_1h'] <= 0,  # Not in uptrend regime
            ])

        dataframe.loc[
            reduce(lambda x, y: x & y, short_conditions),
            'enter_short'
        ] = 1

        return dataframe

    def populate_exit_trend(self, dataframe: DataFrame, metadata: dict) -> DataFrame:
        """Advanced exit logic with profit optimization"""

        # Long Exit Conditions
        long_exit_conditions = [
            # Momentum reversal
            (dataframe['rsi'] > self.rsi_sell_threshold.value) |
            (dataframe['alpha_score'] < -0.1) |
            (dataframe['momentum_persistence'] < 0.4) |

            # Trend reversal
            (dataframe['close'] < dataframe['supertrend']) |
            (dataframe['ema_fast'] < dataframe['ema_slow']) |

            # Volume divergence
            ((dataframe['close'] > dataframe['close'].shift(1)) &
             (dataframe['volume_ratio'] < 0.8)) |

            # Risk management exits
            (dataframe['volatility_rank'] > 0.9) |  # Extreme volatility
            (dataframe['bb_percent'] > 0.95)  # Extreme overbought
        ]

        dataframe.loc[
            reduce(lambda x, y: x | y, long_exit_conditions),
            'exit_long'
        ] = 1

        # Short Exit Conditions
        short_exit_conditions = [
            # Momentum reversal
            (dataframe['rsi'] < (100 - self.rsi_sell_threshold.value)) |
            (dataframe['alpha_score'] > 0.1) |
            (dataframe['momentum_persistence'] > 0.6) |

            # Trend reversal
            (dataframe['close'] > dataframe['supertrend']) |
            (dataframe['ema_fast'] > dataframe['ema_slow']) |

            # Volume divergence
            ((dataframe['close'] < dataframe['close'].shift(1)) &
             (dataframe['volume_ratio'] < 0.8)) |

            # Risk management exits
            (dataframe['volatility_rank'] > 0.9) |  # Extreme volatility
            (dataframe['bb_percent'] < 0.05)  # Extreme oversold
        ]

        dataframe.loc[
            reduce(lambda x, y: x | y, short_exit_conditions),
            'exit_short'
        ] = 1

        return dataframe

    def custom_stoploss(self, pair: str, trade: 'Trade', current_time: datetime,
                       current_rate: float, current_profit: float, **kwargs) -> float:
        """
        Dynamic stoploss based on volatility and trade performance
        """

        # Get current dataframe
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)
        latest_candle = dataframe.iloc[-1]

        # Base stoploss
        base_stoploss = self.stoploss

        # Volatility adjustment
        atr_percent = latest_candle['atr_percent']
        if atr_percent > 0:
            volatility_multiplier = min(2.0, max(0.5, atr_percent / 2.0))
            adjusted_stoploss = base_stoploss * volatility_multiplier
        else:
            adjusted_stoploss = base_stoploss

        # Time-based adjustment (tighter stop as time passes)
        trade_duration = (current_time - trade.open_date).total_seconds() / 3600  # hours
        time_factor = min(1.5, 1.0 + trade_duration / 100)  # Gradually tighten

        # Profit-based adjustment
        if current_profit > 0.02:  # If profit > 2%
            # Implement trailing stop
            trailing_factor = 0.7  # Keep 70% of profits
            dynamic_stop = max(adjusted_stoploss, -(current_profit * trailing_factor))
        else:
            dynamic_stop = adjusted_stoploss * time_factor

        return max(dynamic_stop, -0.15)  # Never exceed 15% loss

    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, rate: float,
                          time_in_force: str, current_time: datetime, entry_tag: Optional[str],
                          side: str, **kwargs) -> bool:
        """
        Advanced trade confirmation with risk management
        """

        # Get current market data
        dataframe, _ = self.dp.get_analyzed_dataframe(pair, self.timeframe)

        if len(dataframe) < 1:
            return False

        latest = dataframe.iloc[-1]

        # Check volatility regime
        if latest['volatility_rank'] > 0.9:
            return False  # Too volatile

        # Check if we're in a strong trend
        if abs(latest['trend_strength']) < self.trend_strength_threshold.value:
            return False  # Trend not strong enough

        # Volume confirmation
        if latest['volume_ratio'] < 1.0:
            return False  # Volume too low

        # Alpha score final check
        if side == 'long' and latest['alpha_score'] < 0.1:
            return False
        elif side == 'short' and latest['alpha_score'] > -0.1:
            return False

        return True

    def check_exit_timeout(self, pair: str, trade: Trade, order: Order,
                          current_time: datetime, **kwargs) -> bool:
        """
        Force exit if trade is open too long without movement
        """
        if (current_time - trade.open_date).total_seconds() > 8 * 3600:  # 8 hours
            return True
        return False


def reduce(function, iterable, initializer=None):
    """Python's reduce function for combining conditions"""
    it = iter(iterable)
    if initializer is None:
        value = next(it)
    else:
        value = initializer
    for element in it:
        value = function(value, element)
    return value