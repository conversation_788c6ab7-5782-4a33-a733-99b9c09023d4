<?php

declare(strict_types=1);

namespace App\Modules\Agent\Services;

use App\Core\Services\BaseService;
use App\Modules\Agent\Models\AgentStoreMapping;
use App\Modules\Agent\Repositories\AgentStoreMappingRepository;
use System\Exception\SystemException;

class AgentStoreMappingService {
   public function __construct(
      private AgentStoreMappingRepository $repository
   ) {
   }

   /**
    * Tüm temsilci-mağaza eşleştirmelerini getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $mappings = $this->repository->getAll();
      return array_map(function ($mapping) {
         return $mapping->jsonSerialize();
      }, $mappings);
   }

   /**
    * ID ile temsilci-mağaza eşleştirmesini getirir.
    *
    * @param int $id
    * @return object|null
    */
   public function getById(int $id): ?object {
      $mapping = $this->repository->getById($id);
      if (!$mapping) {
         throw new SystemException('Mapping not found');
      }
      return $mapping;
   }

   /**
    * Temsilci ID'sine göre mağaza eşleştirmelerini getirir.
    *
    * @param int $agentId
    * @return array
    */
   public function getByAgentId(int $agentId): array {
      $mappings = $this->repository->getByAgentId($agentId);
      return array_map(function ($mapping) {
         return $mapping->jsonSerialize();
      }, $mappings);
   }

   /**
    * Mağaza ID'sine göre temsilci eşleştirmelerini getirir.
    *
    * @param int $storeId
    * @return array
    */
   public function getByStoreId(int $storeId): array {
      $mappings = $this->repository->getByStoreId($storeId);
      return array_map(function ($mapping) {
         return $mapping->jsonSerialize();
      }, $mappings);
   }

   /**
    * Yeni temsilci-mağaza eşleştirmesi oluşturur.
    *
    * @param array $data
    * @return AgentStoreMapping
    */
   public function create(array $data): AgentStoreMapping {
      if ($this->repository->existsByAgentAndStore($data['agent_id'], $data['store_id'])) {
         throw new SystemException('Mapping already exists for this agent and store');
      }
      return $this->repository->create($data);
   }

   /**
    * Temsilci-mağaza eşleştirmesini günceller.
    *
    * @param int $id
    * @param array $data
    * @return object|null
    */
   public function update(int $id, array $data): ?object {
      if (isset($data['agent_id']) && isset($data['store_id'])) {
         if ($this->repository->existsByAgentAndStore($data['agent_id'], $data['store_id'])) {
            $mapping = $this->repository->getById($id);
            if (!$mapping || $mapping->getAgentId() !== $data['agent_id'] || $mapping->getStoreId() !== $data['store_id']) {
               throw new SystemException('Mapping already exists for this agent and store');
            }
         }
      }
      return $this->repository->update($id, $data);
   }

   /**
    * Temsilci-mağaza eşleştirmesini siler.
    *
    * @param int $id
    * @return bool
    */
   public function delete(int $id): bool {
      return $this->repository->delete($id);
   }
}