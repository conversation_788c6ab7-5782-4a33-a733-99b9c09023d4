<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Promotion\Models\TimedPromotion;
use System\Database\Database;
use System\Exception\SystemException;

class TimedPromotionRepository extends BaseRepository {
   protected string $table = 'timed_promotions';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu TimedPromotion nesnesine dönüştürür.
    *
    * @param array $data
    * @return TimedPromotion
    */
   protected function mapToObject(array $data): TimedPromotion {
      return new TimedPromotion(...$data);
   }

   /**
    * Zamanlı promosyon oluşturur.
    *
    * @param array $data
    * @return TimedPromotion
    */
   public function create(array $data): TimedPromotion {
      try {
         $stmt = $this->database->prepare(
            'INSERT INTO timed_promotions (promotion_id, applicable_days, start_time, end_time)
             VALUES (:promotion_id, :applicable_days, :start_time, :end_time)'
         );

         $stmt->execute([
            'promotion_id' => $data['promotion_id'],
            'applicable_days' => $data['applicable_days'],
            'start_time' => $data['start_time'],
            'end_time' => $data['end_time']
         ]);

         $id = $this->database->getLastId();
         return $this->getById((int)$id);
      } catch (\PDOException $e) {
         throw new SystemException('Timed promotion creation failed: ' . $e->getMessage());
      }
   }

   /**
    * Zamanlı promosyonu günceller.
    *
    * @param int $id
    * @param array $data
    * @return TimedPromotion|null
    */
   public function update(int $id, array $data): ?object {
      try {
         $setFields = [];
         $params = ['id' => $id];

         if (isset($data['promotion_id'])) {
            $setFields[] = 'promotion_id = :promotion_id';
            $params['promotion_id'] = $data['promotion_id'];
         }

         if (isset($data['applicable_days'])) {
            $setFields[] = 'applicable_days = :applicable_days';
            $params['applicable_days'] = $data['applicable_days'];
         }

         if (isset($data['start_time'])) {
            $setFields[] = 'start_time = :start_time';
            $params['start_time'] = $data['start_time'];
         }

         if (isset($data['end_time'])) {
            $setFields[] = 'end_time = :end_time';
            $params['end_time'] = $data['end_time'];
         }

         if (empty($setFields)) {
            return null;
         }

         $sql = 'UPDATE timed_promotions SET ' . implode(', ', $setFields) . ' WHERE id = :id';
         $stmt = $this->database->prepare($sql);
         $stmt->execute($params);

         return $stmt->getAffectedRows() > 0 ? $this->getById($id) : null;
      } catch (\PDOException $e) {
         throw new SystemException('Timed promotion update failed: ' . $e->getMessage());
      }
   }

   /**
    * Promosyon ID'sine göre zamanlı promosyonları getirir.
    *
    * @param int $promotionId
    * @return array
    */
   public function getByPromotionId(int $promotionId): array {
      $stmt = $this->database->prepare(
         'SELECT * FROM timed_promotions WHERE promotion_id = :promotion_id'
      );
      $stmt->execute(['promotion_id' => $promotionId]);
      $timedPromotions = $stmt->getAll();

      return array_map(function ($timedPromotion) {
         return $this->mapToObject((array)$timedPromotion);
      }, $timedPromotions);
   }

   /**
    * Şu anda geçerli olan zamanlı promosyonları getirir.
    *
    * @return array
    */
   public function getCurrentlyActiveTimedPromotions(): array {
      $dayOfWeek = strtolower(date('l')); // 'monday', 'tuesday', etc.
      $currentTime = date('H:i:s');

      $stmt = $this->database->prepare(
         'SELECT tp.*, p.*
          FROM timed_promotions tp
          JOIN promotions p ON tp.promotion_id = p.id
          WHERE tp.applicable_days = :day_of_week
          AND tp.start_time <= :current_time
          AND tp.end_time >= :current_time
          AND p.status = "active"
          AND p.start_date <= NOW()
          AND p.end_date >= NOW()'
      );
      $stmt->execute([
         'day_of_week' => $dayOfWeek,
         'current_time' => $currentTime
      ]);
      $timedPromotions = $stmt->getAll();

      return array_map(function ($timedPromotion) {
         return $this->mapToObject((array)$timedPromotion);
      }, $timedPromotions);
   }
}
