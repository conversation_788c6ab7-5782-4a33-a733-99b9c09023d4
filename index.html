<!doctype html>
<html lang="tr-TR">
   <head>
      <meta charset="UTF-8" />
      <meta
         name="viewport"
         content="width=device-width, initial-scale=1.0" />

      <link
         type="image/svg+xml"
         href="/vite.svg"
         rel="icon" />
      <link
         href="https://fonts.googleapis.com"
         rel="preconnect" />
      <link
         href="https://fonts.gstatic.com"
         crossorigin
         rel="preconnect" />
      <link
         href="https://fonts.googleapis.com/css2?family=Roboto:wght@300..900&display=swap"
         rel="stylesheet" />
      <link
         href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@300..900&display=swap"
         rel="stylesheet" />

      <script>
         const theme = localStorage.getItem("app.theme") === "dark";
         document.documentElement.style.backgroundColor = theme ? "#212121" : "#fafafa";
      </script>

      <title>Reserve Test</title>
   </head>
   <body>
      <div id="app"></div>
      <script
         src="/src/main.ts"
         type="module"></script>
   </body>
</html>
