import pandas as pd
from urllib import request
import ssl
from tvDatafeed import TvDatafeed, Interval
from market_profile import MarketProfile

def Hisse_Temel_Veriler():
   url = "https://www.isyatirim.com.tr/tr-tr/analiz/hisse/Sayfalar/Te<PERSON>-Degerler-Ve-Oranlar.aspx#page-1"
   context = ssl._create_unverified_context()
   response = request.urlopen(url, context=context)
   data = pd.read_html(response.read(), decimal=",", thousands=".")
   return data

def calculate_market_profile(data):
   mp = MarketProfile(data)
   mp_slice = mp[data.index.min():data.index.max()]
   POC = mp_slice.poc_price
   VAL, VAH = mp_slice.value_area
   return VAH, VAL, POC