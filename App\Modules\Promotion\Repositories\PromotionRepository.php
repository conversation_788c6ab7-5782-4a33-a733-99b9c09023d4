<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Promotion\Models\Promotion;
use System\Database\Database;
use System\Exception\SystemException;

class PromotionRepository extends BaseRepository {
   protected string $table = 'promotions';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu Promotion nesnesine dönüştürür.
    *
    * @param array $data
    * @return Promotion
    */
   protected function mapToObject(array $data): Promotion {
      return new Promotion(...$data);
   }

   /**
    * Yeni bir promosyon oluşturur.
    *
    * @param array $data
    * @return Promotion
    */
   public function create(array $data): Promotion {
      try {
         $stmt = $this->database->prepare(
            'INSERT INTO promotions (
               code, type, value, start_date, end_date, max_usage,
               user_usage_limit, min_order_amount, applicable_to,
               service_id, store_id, status
            ) VALUES (
               :code, :type, :value, :start_date, :end_date, :max_usage,
               :user_usage_limit, :min_order_amount, :applicable_to,
               :service_id, :store_id, :status
            )'
         );

         $stmt->execute([
            'code' => $data['code'],
            'type' => $data['type'],
            'value' => $data['value'],
            'start_date' => $data['start_date'],
            'end_date' => $data['end_date'],
            'max_usage' => $data['max_usage'] ?? null,
            'user_usage_limit' => $data['user_usage_limit'] ?? 1,
            'min_order_amount' => $data['min_order_amount'] ?? null,
            'applicable_to' => $data['applicable_to'] ?? 'all',
            'service_id' => $data['service_id'] ?? null,
            'store_id' => $data['store_id'] ?? null,
            'status' => $data['status'] ?? 'active'
         ]);

         return $this->getById((int)$this->database->getLastId());
      } catch (\PDOException $e) {
         throw new SystemException('Promotion creation failed: ' . $e->getMessage());
      }
   }

   /**
    * Promosyonu günceller.
    *
    * @param int $id
    * @param array $data
    * @return Promotion|null
    */
   public function update(int $id, array $data): ?object {
      try {
         $setFields = [];
         $params = ['id' => $id];

         if (isset($data['code'])) {
            $setFields[] = 'code = :code';
            $params['code'] = $data['code'];
         }

         if (isset($data['type'])) {
            $setFields[] = 'type = :type';
            $params['type'] = $data['type'];
         }

         if (isset($data['value'])) {
            $setFields[] = 'value = :value';
            $params['value'] = $data['value'];
         }

         if (isset($data['start_date'])) {
            $setFields[] = 'start_date = :start_date';
            $params['start_date'] = $data['start_date'];
         }

         if (isset($data['end_date'])) {
            $setFields[] = 'end_date = :end_date';
            $params['end_date'] = $data['end_date'];
         }

         if (isset($data['max_usage'])) {
            $setFields[] = 'max_usage = :max_usage';
            $params['max_usage'] = $data['max_usage'];
         }

         if (isset($data['user_usage_limit'])) {
            $setFields[] = 'user_usage_limit = :user_usage_limit';
            $params['user_usage_limit'] = $data['user_usage_limit'];
         }

         if (isset($data['min_order_amount'])) {
            $setFields[] = 'min_order_amount = :min_order_amount';
            $params['min_order_amount'] = $data['min_order_amount'];
         }

         if (isset($data['applicable_to'])) {
            $setFields[] = 'applicable_to = :applicable_to';
            $params['applicable_to'] = $data['applicable_to'];
         }

         if (isset($data['service_id'])) {
            $setFields[] = 'service_id = :service_id';
            $params['service_id'] = $data['service_id'];
         }

         if (isset($data['store_id'])) {
            $setFields[] = 'store_id = :store_id';
            $params['store_id'] = $data['store_id'];
         }

         if (isset($data['status'])) {
            $setFields[] = 'status = :status';
            $params['status'] = $data['status'];
         }

         if (empty($setFields)) {
            return null;
         }

         $sql = 'UPDATE promotions SET ' . implode(', ', $setFields) . ' WHERE id = :id';
         $stmt = $this->database->prepare($sql);
         $stmt->execute($params);

         return $stmt->getAffectedRows() > 0 ? $this->getById($id) : null;
      } catch (\PDOException $e) {
         throw new SystemException('Promotion update failed: ' . $e->getMessage());
      }
   }

   /**
    * Promosyon kodunun var olup olmadığını kontrol eder.
    *
    * @param string $code
    * @return bool
    */
   public function existsByCode(string $code): bool {
      return (bool) $this->database->prepare(
         'SELECT 1 FROM promotions
          WHERE code = :code'
      )->execute(['code' => $code])->getRow();
   }

   /**
    * Aktif promosyonları getirir.
    *
    * @return array
    */
   public function getActivePromotions(): array {
      $stmt = $this->database->prepare(
         'SELECT * FROM promotions
          WHERE status = :status
          AND start_date <= NOW()
          AND end_date >= NOW()'
      );
      $stmt->execute(['status' => 'active']);
      $promotions = $stmt->getAll();

      return array_map(function ($promotion) {
         return $this->mapToObject((array)$promotion);
      }, $promotions);
   }

   /**
    * Mağaza ID'sine göre promosyonları getirir.
    *
    * @param int $storeId
    * @return array
    */
   public function getByStoreId(int $storeId): array {
      $stmt = $this->database->prepare(
         'SELECT * FROM promotions WHERE store_id = :store_id'
      );
      $stmt->execute(['store_id' => $storeId]);
      $promotions = $stmt->getAll();

      return array_map(function ($promotion) {
         return $this->mapToObject((array)$promotion);
      }, $promotions);
   }

   /**
    * Hizmet ID'sine göre promosyonları getirir.
    *
    * @param int $serviceId
    * @return array
    */
   public function getByServiceId(int $serviceId): array {
      $stmt = $this->database->prepare(
         'SELECT * FROM promotions WHERE service_id = :service_id'
      );
      $stmt->execute(['service_id' => $serviceId]);
      $promotions = $stmt->getAll();

      return array_map(function ($promotion) {
         return $this->mapToObject((array)$promotion);
      }, $promotions);
   }

   /**
    * Promosyon koduna göre promosyonu getirir.
    *
    * @param string $code
    * @return Promotion|null
    */
   public function getByCode(string $code): ?Promotion {
      $stmt = $this->database->prepare(
         'SELECT * FROM promotions WHERE code = :code'
      );
      $stmt->execute(['code' => $code]);
      $promotion = $stmt->getRow();

      if (!$promotion) {
         return null;
      }

      return $this->mapToObject((array)$promotion);
   }
}
