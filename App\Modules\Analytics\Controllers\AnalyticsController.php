<?php

declare(strict_types=1);

namespace App\Modules\Analytics\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Analytics\Services\AnalyticsService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Analytics", description="Analitik Yönetimi")
 */
class AnalyticsController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private AnalyticsService $analyticsService
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Post(
    *     path="/api/analytics/events/track",
    *     summary="Analitik olay kaydeder",
    *     tags={"Analytics"},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"event_type"},
    *             @OA\Property(property="event_type", type="string", example="page_view"),
    *             @OA\Property(property="user_id", type="integer", example=1),
    *             @OA\Property(property="entity_type", type="string", example="article"),
    *             @OA\Property(property="entity_id", type="integer", example=123),
    *             @OA\Property(property="metadata", type="object", example={"page": "/home", "referrer": "google.com"})
    *         )
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="400", description="Geçersiz istek"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function trackEvent() {
      $data = $this->request->json();

      if (empty($data['event_type'])) {
         return $this->error('Event type is required');
      }

      $event = $this->analyticsService->trackEvent($data['event_type'], $data);
      return $this->success($event, 'Event tracked successfully');
   }

   /**
    * @OA\Post(
    *     path="/api/analytics/metrics/record",
    *     summary="Metrik kaydeder veya günceller",
    *     tags={"Analytics"},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"metric_name", "value"},
    *             @OA\Property(property="metric_name", type="string", example="page_views"),
    *             @OA\Property(property="value", type="number", format="float", example=1.0),
    *             @OA\Property(property="dimension", type="string", example="page"),
    *             @OA\Property(property="dimension_value", type="string", example="/home"),
    *             @OA\Property(property="period_type", type="string", enum={"hourly", "daily", "weekly", "monthly", "yearly"}, example="daily"),
    *             @OA\Property(property="period_start", type="string", format="date-time", example="2023-01-01 00:00:00"),
    *             @OA\Property(property="period_end", type="string", format="date-time", example="2023-01-01 23:59:59")
    *         )
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="400", description="Geçersiz istek"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function recordMetric() {
      $data = $this->request->json();

      if (empty($data['metric_name']) || !isset($data['value'])) {
         return $this->error('Metric name and value are required');
      }

      $options = [
         'dimension' => $data['dimension'] ?? null,
         'dimension_value' => $data['dimension_value'] ?? null,
         'period_type' => $data['period_type'] ?? 'daily',
         'period_start' => $data['period_start'] ?? date('Y-m-d 00:00:00'),
         'period_end' => $data['period_end'] ?? date('Y-m-d 23:59:59')
      ];

      $metric = $this->analyticsService->recordMetric($data['metric_name'], (float) $data['value'], $options);
      return $this->success($metric, 'Metric recorded successfully');
   }

   /**
    * @OA\Post(
    *     path="/api/analytics/user-activity/track",
    *     summary="Kullanıcı aktivitesi kaydeder",
    *     tags={"Analytics"},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             required={"user_id", "activity_type"},
    *             @OA\Property(property="user_id", type="integer", example=1),
    *             @OA\Property(property="activity_type", type="string", example="login"),
    *             @OA\Property(property="metadata", type="object", example={"ip": "***********", "device": "mobile"})
    *         )
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="400", description="Geçersiz istek"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function trackUserActivity() {
      $data = $this->request->json();

      if (empty($data['user_id']) || empty($data['activity_type'])) {
         return $this->error('User ID and activity type are required');
      }

      $activity = $this->analyticsService->trackUserActivity(
         (int) $data['user_id'],
         $data['activity_type'],
         $data['metadata'] ?? null
      );

      return $this->success($activity, 'User activity tracked successfully');
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/events/user/{userId}",
    *     summary="Kullanıcının analitik olaylarını getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="userId",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Parameter(
    *         name="event_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     @OA\Response(response="404", description="Kullanıcı bulunamadı"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getUserEvents(int $userId) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $filters = [
         'event_type' => $this->request->get('event_type'),
         'start_date' => $this->request->get('start_date'),
         'end_date' => $this->request->get('end_date'),
         'limit' => $this->request->get('limit')
      ];

      $events = $this->analyticsService->getUserEvents($userId, array_filter($filters));
      return $this->success($events);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/user-activity/{userId}",
    *     summary="Kullanıcının aktivitelerini getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="userId",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     @OA\Response(response="404", description="Kullanıcı bulunamadı"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getUserActivities(int $userId) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $activities = $this->analyticsService->getUserActivities($userId);
      return $this->success($activities);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/metrics/trend/{metricName}",
    *     summary="Metrik trendi getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="metricName",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="period_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", enum={"hourly", "daily", "weekly", "monthly", "yearly"})
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="dimension",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="dimension_value",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getMetricTrend(string $metricName) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $options = [
         'period_type' => $this->request->get('period_type', 'daily'),
         'start_date' => $this->request->get('start_date', date('Y-m-d 00:00:00', strtotime('-30 days'))),
         'end_date' => $this->request->get('end_date', date('Y-m-d 23:59:59')),
         'dimension' => $this->request->get('dimension'),
         'dimension_value' => $this->request->get('dimension_value')
      ];

      $trend = $this->analyticsService->getMetricTrend($metricName, array_filter($options));
      return $this->success($trend);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/metrics/{metricName}",
    *     summary="Metrik adına göre metrikleri getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="metricName",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="period_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", enum={"hourly", "daily", "weekly", "monthly", "yearly"})
    *     ),
    *     @OA\Parameter(
    *         name="dimension",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="dimension_value",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getMetricsByName(string $metricName) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $filters = [
         'period_type' => $this->request->get('period_type'),
         'dimension' => $this->request->get('dimension'),
         'dimension_value' => $this->request->get('dimension_value'),
         'start_date' => $this->request->get('start_date'),
         'end_date' => $this->request->get('end_date'),
         'limit' => $this->request->get('limit')
      ];

      $metrics = $this->analyticsService->getMetricsByName($metricName, array_filter($filters));
      return $this->success($metrics);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/metrics/dimension/{dimension}/{value}",
    *     summary="Boyut ve değere göre metrikleri getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="dimension",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="value",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="metric_name",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="period_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", enum={"hourly", "daily", "weekly", "monthly", "yearly"})
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getMetricsByDimension(string $dimension, string $value) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $filters = [
         'metric_name' => $this->request->get('metric_name'),
         'period_type' => $this->request->get('period_type'),
         'start_date' => $this->request->get('start_date'),
         'end_date' => $this->request->get('end_date'),
         'limit' => $this->request->get('limit')
      ];

      $metrics = $this->analyticsService->getMetricsByDimension($dimension, $value, array_filter($filters));
      return $this->success($metrics);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/users/most-active/{activityType}",
    *     summary="En aktif kullanıcıları getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="activityType",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getMostActiveUsers(string $activityType) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $limit = (int) $this->request->get('limit', 10);
      $users = $this->analyticsService->getMostActiveUsers($activityType, $limit);
      return $this->success($users);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/users/recently-active",
    *     summary="Son aktif olan kullanıcıları getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getRecentlyActiveUsers() {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $limit = (int) $this->request->get('limit', 10);
      $users = $this->analyticsService->getRecentlyActiveUsers($limit);
      return $this->success($users);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/users/active-count",
    *     summary="Aktif kullanıcı sayısını getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=true,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=true,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="activity_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function countActiveUsers() {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $startDate = $this->request->get('start_date');
      $endDate = $this->request->get('end_date');
      $activityType = $this->request->get('activity_type');

      if (!$startDate || !$endDate) {
         return $this->error('Start date and end date are required');
      }

      $count = $this->analyticsService->countActiveUsers($startDate, $endDate, $activityType);
      return $this->success(['count' => $count]);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/events/type/{eventType}",
    *     summary="Olay türüne göre olayları getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="eventType",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="user_id",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Parameter(
    *         name="entity_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getEventsByType(string $eventType) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $filters = [
         'user_id' => $this->request->get('user_id'),
         'entity_type' => $this->request->get('entity_type'),
         'start_date' => $this->request->get('start_date'),
         'end_date' => $this->request->get('end_date'),
         'limit' => $this->request->get('limit')
      ];

      $events = $this->analyticsService->getEventsByType($eventType, array_filter($filters));
      return $this->success($events);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/events/entity/{entityType}/{entityId}",
    *     summary="Varlık türü ve ID'sine göre olayları getirir",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="entityType",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="entityId",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Parameter(
    *         name="event_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="limit",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function getEventsByEntity(string $entityType, int $entityId) {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $filters = [
         'event_type' => $this->request->get('event_type'),
         'start_date' => $this->request->get('start_date'),
         'end_date' => $this->request->get('end_date'),
         'limit' => $this->request->get('limit')
      ];

      $events = $this->analyticsService->getEventsByEntity($entityType, $entityId, array_filter($filters));
      return $this->success($events);
   }

   /**
    * @OA\Get(
    *     path="/api/analytics/events/count",
    *     summary="Olayları sayar",
    *     tags={"Analytics"},
    *     @OA\Parameter(
    *         name="event_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="user_id",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Parameter(
    *         name="entity_type",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Parameter(
    *         name="entity_id",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Parameter(
    *         name="start_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Parameter(
    *         name="end_date",
    *         in="query",
    *         required=false,
    *         @OA\Schema(type="string", format="date-time")
    *     ),
    *     @OA\Response(response="200", description="Başarılı"),
    *     @OA\Response(response="401", description="Yetkisiz erişim"),
    *     security={{"Bearer": {}}}
    * )
    */
   public function countEvents() {
      if (!$this->hasRole(['admin', 'manager'])) {
         return $this->forbidden('You do not have permission to access this resource');
      }

      $filters = [
         'event_type' => $this->request->get('event_type'),
         'user_id' => $this->request->get('user_id'),
         'entity_type' => $this->request->get('entity_type'),
         'entity_id' => $this->request->get('entity_id'),
         'start_date' => $this->request->get('start_date'),
         'end_date' => $this->request->get('end_date')
      ];

      $count = $this->analyticsService->countEvents(array_filter($filters));
      return $this->success(['count' => $count]);
   }
}
