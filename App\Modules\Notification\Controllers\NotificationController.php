<?php

declare(strict_types=1);

namespace App\Modules\Notification\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Notification\Services\NotificationService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Notification", description="Bildirim işlemleri")
 */
class NotificationController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private NotificationService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Notification"},
    *     path="/api/notifications",
    *     summary="Tüm Bildirimleri Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Bildirim listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
    public function getAll() {
      $notifications = $this->service->getAll();
      return $this->success($notifications);
   }

   /**
    * @OA\Get(
    *     tags={"Notification"},
    *     path="/api/notifications/booking/{id}",
    *     summary="Rezervasyon Bildirimlerini Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Bildirim listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=404, description="Notifications not found"),
    * )
    */
   public function getByBookingId(int $id) {
      $notifications = $this->service->getByBookingId($id);
      return $this->success($notifications);
   }

   /**
    * @OA\Post(
    *     tags={"Notification"},
    *     path="/api/notifications/send",
    *     summary="Bildirim Gönder",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(required=true,
    *         @OA\MediaType(mediaType="application/json",
    *             @OA\Schema(required={"booking_id", "notify_type"},
    *                 @OA\Property(property="booking_id", type="integer", example=1),
    *                 @OA\Property(property="notify_type", type="string", example="email", enum={"email", "sms", "push"}),
    *                 @OA\Property(property="custom_message", type="string", example="Rezervasyonunuz onaylandı")
    *             )
    *         )
    *     ),
    *     @OA\Response(response=200, description="Bildirim gönderildi"),
    *     @OA\Response(response=400, description="Bad Request"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function send() {
      $data = $this->request->json();

      if (!isset($data['booking_id']) || !isset($data['notify_type'])) {
         return $this->error('Missing required fields');
      }

      $result = $this->service->sendNotification(
         (int)$data['booking_id'],
         $data['notify_type'],
         $data['custom_message'] ?? null
      );

      return $this->success($result);
   }

   /**
    * @OA\Get(
    *     tags={"Notification"},
    *     path="/api/notifications/user",
    *     summary="Kullanıcı Bildirimlerini Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Bildirim listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByUserId() {
      $user = $this->auth->getUser();

      if (!$user) {
         return $this->forbidden();
      }

      $notifications = $this->service->getByUserId($user['id']);
      return $this->success($notifications);
   }

   /**
    * @OA\Post(
    *     tags={"Notification"},
    *     path="/api/notifications/resend/{id}",
    *     summary="Bildirimi Yeniden Gönder",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Bildirim yeniden gönderildi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=404, description="Notification not found"),
    * )
    */
   public function resend(int $id) {
      $result = $this->service->resendNotification($id);
      return $this->success($result);
   }
}
