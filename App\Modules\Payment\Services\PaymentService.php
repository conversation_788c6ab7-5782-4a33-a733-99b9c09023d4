<?php

declare(strict_types=1);

namespace App\Modules\Payment\Services;

use App\Core\Validation\Validator;
use App\Modules\Payment\Models\Payment;
use App\Modules\Payment\Repositories\PaymentRepository;
use System\Exception\SystemException;
use System\Http\Request;

class PaymentService {
   // Define validation rules based on Payment model
   private array $validationRules = [
      'booking_id' => ['required', 'numeric'],
      'payment_method' => ['required', 'in:credit_card,cash,bank_transfer'],
      'amount' => ['required', 'numeric'],
      'status' => ['in:pending,completed,failed'],
      'payment_date' => ['date']
   ];

   public function __construct(
      private PaymentRepository $paymentRepository,
      private Request $request,
      private Validator $validator // Inject Validator
   ) {

   }

   /**
    * Tüm ödemeleri getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $payments = $this->paymentRepository->getAll();
      return array_map(function ($payment) {
         return $payment->jsonSerialize();
      }, $payments);
   }

   /**
    * ID ile ödeme getirir.
    *
    * @param int $id
    * @return array
    */
   public function getById(int $id): array {
      $payment = $this->paymentRepository->getById($id);
      if (!$payment) {
         throw new SystemException('Ödeme bulunamadı');
      }
      return $payment->jsonSerialize();
   }

   /**
    * Rezervasyon ID'sine göre ödemeleri getirir.
    *
    * @param int $bookingId
    * @return array
    */
   public function getByBookingId(int $bookingId): array {
      $payments = $this->paymentRepository->findByBookingId($bookingId);
      return array_map(function ($payment) {
         return $payment->jsonSerialize();
      }, $payments);
   }

   /**
    * Ödeme durumuna göre ödemeleri getirir.
    *
    * @param string $status
    * @return array
    */
   public function getByStatus(string $status): array {
      $payments = $this->paymentRepository->findByStatus($status);
      return array_map(function ($payment) {
         return $payment->jsonSerialize();
      }, $payments);
   }

   /**
    * Ödeme yöntemine göre ödemeleri getirir.
    *
    * @param string $paymentMethod
    * @return array
    */
   public function getByPaymentMethod(string $paymentMethod): array {
      $payments = $this->paymentRepository->findByPaymentMethod($paymentMethod);
      return array_map(function ($payment) {
         return $payment->jsonSerialize();
      }, $payments);
   }

   /**
    * Belirli bir tarih aralığındaki ödemeleri getirir.
    *
    * @param string $startDate
    * @param string $endDate
    * @return array
    */
   public function getByDateRange(string $startDate, string $endDate): array {
      $payments = $this->paymentRepository->findByDateRange($startDate, $endDate);
      return array_map(function ($payment) {
         return $payment->jsonSerialize();
      }, $payments);
   }

   /**
    * Yeni ödeme oluşturur.
    *
    * @param array $data
    * @return Payment
    */
   public function create(array $data): Payment {
      // Varsayılan değerleri ayarla
      if (!isset($data['status'])) {
         $data['status'] = 'pending';
      }

      // Validasyon işlemi
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validasyon hatası: ' . json_encode($this->validator->getErrors()));
      }

      // Ödeme tutarı kontrolü (0'dan büyük olmalı)
      if (isset($data['amount']) && $data['amount'] <= 0) {
         throw new SystemException('Geçersiz ödeme tutarı: 0`dan büyük olmalıdır');
      }

      // Ödeme oluştur
      $payment = $this->paymentRepository->create($data);
      if (!$payment) {
         throw new SystemException('Ödeme oluşturulamadı');
      }

      return $payment;
   }

   /**
    * Ödeme durumunu günceller.
    *
    * @param int $id
    * @param string $status
    * @return array
    */
   public function updateStatus(int $id, string $status): array {
      // Ödeme kontrolü
      $payment = $this->paymentRepository->getById($id);
      if (!$payment) {
         throw new SystemException('Ödeme bulunamadı');
      }

      // Durum kontrolü
      $validStatuses = ['pending', 'completed', 'failed'];
      if (!in_array($status, $validStatuses)) {
         throw new SystemException('Geçersiz ödeme durumu');
      }

      // Durumu güncelle
      $result = $this->paymentRepository->update($id, ['status' => $status]);
      if (!$result) {
         throw new SystemException('Ödeme durumu güncellenemedi');
      }

      // Güncellenmiş ödemeyi getir
      $updatedPayment = $this->paymentRepository->getById($id);
      return $updatedPayment->jsonSerialize();
   }

   /**
    * Ödeme bilgilerini günceller.
    *
    * @param int $id
    * @param array $data
    * @return array
    */
   public function update(int $id, array $data): array {
      // Ödeme kontrolü
      $payment = $this->paymentRepository->getById($id);
      if (!$payment) {
         throw new SystemException('Ödeme bulunamadı');
      }

      // Güncellenebilir alanlar
      $updatableFields = ['payment_method', 'amount', 'status'];
      $updateData = [];

      foreach ($updatableFields as $field) {
         if (isset($data[$field])) {
            // Ödeme yöntemi kontrolü
            if ($field === 'payment_method') {
               $validPaymentMethods = ['credit_card', 'cash', 'bank_transfer'];
               if (!in_array($data[$field], $validPaymentMethods)) {
                  throw new SystemException('Geçersiz ödeme yöntemi');
               }
            }

            // Ödeme tutarı kontrolü
            if ($field === 'amount' && (!is_numeric($data[$field]) || $data[$field] <= 0)) {
               throw new SystemException('Geçersiz ödeme tutarı');
            }

            // Ödeme durumu kontrolü
            if ($field === 'status') {
               $validStatuses = ['pending', 'completed', 'failed'];
               if (!in_array($data[$field], $validStatuses)) {
                  throw new SystemException('Geçersiz ödeme durumu');
               }
            }

            $updateData[$field] = $data[$field];
         }
      }

      if (empty($updateData)) {
         throw new SystemException('Güncellenecek veri bulunamadı');
      }

      // Güncelleme işlemi
      $result = $this->paymentRepository->update($id, $updateData);
      if (!$result) {
         throw new SystemException('Ödeme güncellenemedi');
      }

      // Güncellenmiş ödemeyi getir
      $updatedPayment = $this->paymentRepository->getById($id);
      return $updatedPayment->jsonSerialize();
   }

   /**
    * Ödemeyi siler.
    *
    * @param int $id
    * @return bool
    */
   public function delete(int $id): bool {
      // Ödeme kontrolü
      $payment = $this->paymentRepository->getById($id);
      if (!$payment) {
         throw new SystemException('Ödeme bulunamadı');
      }

      // Silme işlemi
      $result = $this->paymentRepository->delete($id);
      if (!$result) {
         throw new SystemException('Ödeme silinemedi');
      }

      return true;
   }
}