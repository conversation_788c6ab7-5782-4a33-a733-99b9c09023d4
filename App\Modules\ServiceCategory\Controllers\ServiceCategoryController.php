<?php

declare(strict_types=1);

namespace App\Modules\ServiceCategory\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\ServiceCategory\Services\ServiceCategoryService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Service Categories", description="Hizmet Kategorileri Yönetimi")
 */
class ServiceCategoryController extends BaseController {
    public function __construct(
        private ServiceCategoryService $service,
        Response $response,
        Request $request,
        Auth $auth,
        CheckRole $checkRole,
    ) {
        parent::__construct($request, $response, $auth, $checkRole);
    }

    /**
     * @OA\Get(
     *     path="/api/service-categories",
     *     summary="Tüm kategorileri listele",
     *     tags={"Service Categories"},
     *     security={{"Bearer": {}}},
     *     @OA\Response(response=200, description="Kategori listesi"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="Category not found")
     * )
     */
    public function getAll() {
        $categories = $this->service->getAll();
        return $this->success($categories);
    }

    /**
     * @OA\Get(
     *     path="/api/service-categories/{id}",
     *     summary="Kategori detaylarını getir",
     *     tags={"Service Categories"},
     *     security={{"Bearer": {}}},
     *     @OA\Response(response=200, description="Kategori detayları"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=404, description="Category not found")
     * )
     */
    public function getById(int $id) {
        $category = $this->service->getById($id);
        return $this->success($category);
    }

    /**
     * @OA\Post(
     *      tags={"Service Categories"},
     *      path="/api/service-categories/create",
     *      summary="Yeni kategori oluştur",
     *      operationId="createServiceCategory",
     *      security={{"Bearer": {}}},
     * @OA\Response(
     *    response=200,
     *    description="Kategori oluşturuldu",
     *      @OA\JsonContent(
     *         @OA\Property(property="status", type="integer", example=201),
     *         @OA\Property(property="message", type="string", example="Category created"),
     *         @OA\Property(
     *            property="data",
     *            @OA\Property(property="id", type="integer", example=123),
     *            @OA\Property(property="name", type="string", example="ServiceCategory"),
     *            @OA\Property(property="description", type="string", example="Service category description")
     *         )
     *      )
     * ),
     * @OA\Response(response=400,description="Validasyon Hatası"),
     * @OA\Response(response=409,description="Çakışma Hatası"),
     * @OA\RequestBody(required=true,
     *    @OA\MediaType(mediaType="application/json",
     *    @OA\Schema(required={"name", "description"},
     *       @OA\Property(property="name", type="string", example="ServiceCategory"),
     *       @OA\Property(property="description", type="string", example="Service category description")
     *    ))
     * ))
     */
    public function create() {
        $data = $this->request->json();
        $category = $this->service->create($data);
        return $this->success([
            'id' => $category->getId(),
            'name' => $category->getName(),
            'description' => $category->getDescription()
        ]);
    }

    /**
     * @OA\Patch(
     *    tags={"Service Categories"},
     *    path="/api/service-categories/update/",
     *    summary="ServiceCategory Güncelle",
     *    operationId="updateServiceCategory",
     *    security={{"Bearer": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="id", type="integer", example=1),
     *             @OA\Property(property="name", type="string", example="serviceCategory"),
     *             @OA\Property(property="description", type="string", example="Lorem ipsum"),
     *          )
     *     ),
     *     @OA\Response(response=200, description="ServiceCategory güncellendi"),
     *     @OA\Response(response=422, description="Validation error")
     * )
     */
    public function update() {
        $data = $this->request->json();
        $category = $this->service->update($data);
        return $this->success($category);
    }

    /**
     * @OA\Delete(
     *     tags={"Service Categories"},
     *     path="/api/service-categories/delete/{id}",
     *     summary="Kategori sil",
     *     security={{"Bearer": {}}},
     *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
     *     @OA\Response(response=200, description="Kategori silindi")
     * )
     */
    public function delete(int $id) {
        $this->service->delete($id);
        return $this->success();
    }
}
