import AuthLayout from "@/assets/layouts/Auth/Layout.vue";
import { getComponent } from "@/utils/helper";
import { RouteRecordRaw } from "vue-router";

export const authRoutes: RouteRecordRaw[] = [
   {
      path: "/login",
      name: "login",
      meta: {
         title: "Login",
         breadcrumb: "Login",
         auth: false,
         layout: AuthLayout
      },
      component: getComponent(() => import("../pages/Login.vue"))
   },
   {
      path: "/register",
      name: "register",
      meta: {
         title: "Register",
         breadcrumb: "Register",
         auth: false,
         layout: AuthLayout
      },
      component: getComponent(() => import("../pages/Login.vue")) // Şimdilik Login sayfasına yönlendirildi, ileride Register sayfası eklenebilir
   }
];