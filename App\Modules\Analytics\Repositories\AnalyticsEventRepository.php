<?php

declare(strict_types=1);

namespace App\Modules\Analytics\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Analytics\Models\AnalyticsEvent;
use System\Database\Database;
use System\Exception\SystemException;

class AnalyticsEventRepository extends BaseRepository {
   protected string $table = 'analytics_events';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu AnalyticsEvent nesnesine dönüştürür.
    *
    * @param array $data
    * @return AnalyticsEvent
    */
   protected function mapToObject(array $data): AnalyticsEvent {
      return new AnalyticsEvent(...$data);
   }

   /**
    * Yeni bir analitik olay kaydeder.
    *
    * @param array $data
    * @return AnalyticsEvent
    */
   public function create(array $data): AnalyticsEvent {
      try {
         $stmt = $this->database->prepare(
            "INSERT INTO {$this->table}
            (event_type, user_id, entity_type, entity_id, metadata, ip_address, user_agent)
            VALUES (:event_type, :user_id, :entity_type, :entity_id, :metadata, :ip_address, :user_agent)"
         );

         $params = [
            'event_type' => $data['event_type'],
            'user_id' => $data['user_id'] ?? null,
            'entity_type' => $data['entity_type'] ?? null,
            'entity_id' => $data['entity_id'] ?? null,
            'metadata' => isset($data['metadata']) ? json_encode($data['metadata']) : null,
            'ip_address' => $data['ip_address'] ?? null,
            'user_agent' => $data['user_agent'] ?? null
         ];

         $stmt->execute($params);
         $id = (int) $this->database->getLastId();

         return $this->getById($id);
      } catch (\Exception $e) {
         throw new SystemException('Failed to create analytics event: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir kullanıcının analitik olaylarını getirir.
    *
    * @param int $userId
    * @param array $filters
    * @return array
    */
   public function getByUserId(int $userId, array $filters = []): array {
      $sql = "SELECT * FROM {$this->table} WHERE user_id = :user_id";
      $params = ['user_id' => $userId];

      if (!empty($filters['event_type'])) {
         $sql .= " AND event_type = :event_type";
         $params['event_type'] = $filters['event_type'];
      }

      if (!empty($filters['entity_type'])) {
         $sql .= " AND entity_type = :entity_type";
         $params['entity_type'] = $filters['entity_type'];
      }

      if (!empty($filters['start_date'])) {
         $sql .= " AND created_at >= :start_date";
         $params['start_date'] = $filters['start_date'];
      }

      if (!empty($filters['end_date'])) {
         $sql .= " AND created_at <= :end_date";
         $params['end_date'] = $filters['end_date'];
      }

      $sql .= " ORDER BY created_at DESC";

      if (!empty($filters['limit'])) {
         $sql .= " LIMIT :limit";
         $params['limit'] = (int) $filters['limit'];
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $events = $stmt->getAll();

      return array_map(function ($event) {
         return $this->mapToObject((array) $event);
      }, $events);
   }

   /**
    * Belirli bir olay türüne göre analitik olayları getirir.
    *
    * @param string $eventType
    * @param array $filters
    * @return array
    */
   public function getByEventType(string $eventType, array $filters = []): array {
      $sql = "SELECT * FROM {$this->table} WHERE event_type = :event_type";
      $params = ['event_type' => $eventType];

      if (!empty($filters['user_id'])) {
         $sql .= " AND user_id = :user_id";
         $params['user_id'] = $filters['user_id'];
      }

      if (!empty($filters['entity_type'])) {
         $sql .= " AND entity_type = :entity_type";
         $params['entity_type'] = $filters['entity_type'];
      }

      if (!empty($filters['start_date'])) {
         $sql .= " AND created_at >= :start_date";
         $params['start_date'] = $filters['start_date'];
      }

      if (!empty($filters['end_date'])) {
         $sql .= " AND created_at <= :end_date";
         $params['end_date'] = $filters['end_date'];
      }

      $sql .= " ORDER BY created_at DESC";

      if (!empty($filters['limit'])) {
         $sql .= " LIMIT :limit";
         $params['limit'] = (int) $filters['limit'];
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $events = $stmt->getAll();

      return array_map(function ($event) {
         return $this->mapToObject((array) $event);
      }, $events);
   }

   /**
    * Belirli bir varlık türü ve ID'sine göre analitik olayları getirir.
    *
    * @param string $entityType
    * @param int $entityId
    * @param array $filters
    * @return array
    */
   public function getByEntity(string $entityType, int $entityId, array $filters = []): array {
      $sql = "SELECT * FROM {$this->table} WHERE entity_type = :entity_type AND entity_id = :entity_id";
      $params = [
         'entity_type' => $entityType,
         'entity_id' => $entityId
      ];

      if (!empty($filters['event_type'])) {
         $sql .= " AND event_type = :event_type";
         $params['event_type'] = $filters['event_type'];
      }

      if (!empty($filters['start_date'])) {
         $sql .= " AND created_at >= :start_date";
         $params['start_date'] = $filters['start_date'];
      }

      if (!empty($filters['end_date'])) {
         $sql .= " AND created_at <= :end_date";
         $params['end_date'] = $filters['end_date'];
      }

      $sql .= " ORDER BY created_at DESC";

      if (!empty($filters['limit'])) {
         $sql .= " LIMIT :limit";
         $params['limit'] = (int) $filters['limit'];
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $events = $stmt->getAll();

      return array_map(function ($event) {
         return $this->mapToObject((array) $event);
      }, $events);
   }

   /**
    * Belirli bir zaman aralığındaki analitik olaylarını sayar.
    *
    * @param array $filters
    * @return int
    */
   public function countEvents(array $filters = []): int {
      $sql = "SELECT COUNT(*) as count FROM {$this->table} WHERE 1=1";
      $params = [];

      if (!empty($filters['event_type'])) {
         $sql .= " AND event_type = :event_type";
         $params['event_type'] = $filters['event_type'];
      }

      if (!empty($filters['user_id'])) {
         $sql .= " AND user_id = :user_id";
         $params['user_id'] = $filters['user_id'];
      }

      if (!empty($filters['entity_type'])) {
         $sql .= " AND entity_type = :entity_type";
         $params['entity_type'] = $filters['entity_type'];
      }

      if (!empty($filters['entity_id'])) {
         $sql .= " AND entity_id = :entity_id";
         $params['entity_id'] = $filters['entity_id'];
      }

      if (!empty($filters['start_date'])) {
         $sql .= " AND created_at >= :start_date";
         $params['start_date'] = $filters['start_date'];
      }

      if (!empty($filters['end_date'])) {
         $sql .= " AND created_at <= :end_date";
         $params['end_date'] = $filters['end_date'];
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $result = $stmt->getRow();

      return (int) ($result->count ?? 0);
   }
}
