<?php

declare(strict_types=1);

use App\Core\Middlewares\Swagger;
use App\Modules\Agent\Controllers\AgentController;
use App\Modules\Agent\Controllers\AgentStoreMappingController;
use App\Modules\AuditLog\Controllers\AuditLogController;
use App\Modules\Booking\Controllers\BookingController;
use App\Modules\Employee\Controllers\EmployeeController;
use App\Modules\Notification\Controllers\NotificationController;
use App\Modules\Payment\Controllers\PaymentController;
use App\Modules\Promotion\Controllers\PromotionController;
use App\Modules\Promotion\Controllers\TimedPromotionController;
use App\Modules\Promotion\Controllers\UserPromotionController;
use App\Modules\Report\Controllers\GeneratedReportController;
use App\Modules\Review\Controllers\ReviewController;
use App\Modules\Service\Controllers\ServiceController;
use App\Modules\ServiceCategory\Controllers\ServiceCategoryController;
use App\Modules\Store\Controllers\StoreController;
use App\Modules\User\Controllers\AuthController;
use App\Modules\User\Controllers\UserProfileController;
use System\Starter\Starter;

$route = Starter::router();

// user routes
$route->prefix('api')->group(function () use ($route) {
	$route->post('/login', [AuthController::class, 'login']);
	$route->post('/register', [UserProfileController::class, 'create']);
	$route->post('/password/reset/request', [UserProfileController::class, 'requestReset']);
	$route->post('/password/reset', [UserProfileController::class, 'resetPassword']);
	$route->post('/logout', [AuthController::class, 'logout']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/users', [UserProfileController::class, 'getAll']);
	$route->delete('/users/delete/{id}', [UserProfileController::class, 'delete']);
	$route->get('/users/{id}', [UserProfileController::class, 'getById']);
	$route->get('/profile', [UserProfileController::class, 'getProfile']);
	$route->patch('/profile/update', [UserProfileController::class, 'update']);
	$route->post('/role/set', [AuthController::class, 'setRole']);
	$route->get('/role/list', [AuthController::class, 'getRoles']);
	$route->get('/role/get/{user_id}', [AuthController::class, 'getRoleById']);
	$route->get('/role/check/{user_id}', [AuthController::class, 'checkRole']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->post('/audit-logs', [AuditLogController::class, 'getAll']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/stores/user', [StoreController::class, 'getByOwner']);
	$route->post('/stores/create', [StoreController::class, 'create']);
	$route->patch('/stores/update', [StoreController::class, 'update']);
	$route->delete('/stores/delete/{id}', [StoreController::class, 'delete']);
});

$route->prefix('api')->group(function () use ($route) {
	$route->get('/stores/view/{id}', [StoreController::class, 'getById']);
	$route->get('/stores', [StoreController::class, 'getAll']);
	$route->get('/stores/{user_id}', [StoreController::class, 'getByUserId']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/service-categories', [ServiceCategoryController::class, 'getAll']);
	$route->get('/service-categories/{id}', [ServiceCategoryController::class, 'getById']);
	$route->post('/service-categories/create', [ServiceCategoryController::class, 'create']);
	$route->patch('/service-categories/update', [ServiceCategoryController::class, 'update']);
	$route->delete('/service-categories/delete/{id}', [ServiceCategoryController::class, 'delete']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/services', [ServiceController::class, 'getAll']);
	$route->get('/services/{id}', [ServiceController::class, 'getById']);
	$route->post('/services/create', [ServiceController::class, 'create']);
	$route->patch('/services/update', [ServiceController::class, 'update']);
	$route->delete('/services/delete/{id}', [ServiceController::class, 'delete']);
	$route->get('/services/view/{id}', [ServiceController::class, 'getById']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/notifications', [NotificationController::class, 'getAll']);
	$route->get('/notifications/booking/{id}', [NotificationController::class, 'getByBookingId']);
	$route->post('/notifications/send', [NotificationController::class, 'send']);
	$route->get('/notifications/user', [NotificationController::class, 'getByUserId']);
	$route->post('/notifications/resend/{id}', [NotificationController::class, 'resend']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/employees/all', [EmployeeController::class, 'getAll']);
	$route->get('/employees/{id}', [EmployeeController::class, 'getById']);
	$route->post('/employees/create', [EmployeeController::class, 'create']);
	$route->patch('/employees/update', [EmployeeController::class, 'update']);
	$route->delete('/employees/delete/{id}', [EmployeeController::class, 'delete']);
	$route->get('/stores/{storeId}/employees', [EmployeeController::class, 'getByStoreId']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	// Spesifik route'lar önce
	$route->get('/bookings/customer', [BookingController::class, 'getCustomerBookings']);
	$route->get('/bookings/store/{storeId}', [BookingController::class, 'getStoreBookings']);
	$route->get('/bookings/employee/{employeeId}', [BookingController::class, 'getEmployeeBookings']);
	$route->get('/bookings/status/{status}', [BookingController::class, 'getBookingsByStatus']);

	// Genel route'lar sonra
	$route->get('/bookings', [BookingController::class, 'getAll']);
	$route->get('/bookings/{id}', [BookingController::class, 'getById']);
	$route->post('/bookings/create', [BookingController::class, 'create']);
	$route->put('/bookings/update/{id}', [BookingController::class, 'update']);
	$route->put('/bookings/status/{id}', [BookingController::class, 'updateStatus']);
	$route->delete('/bookings/delete/{id}', [BookingController::class, 'delete']);
	$route->get('/bookings/availability', [BookingController::class, 'checkAvailability']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/payments', [PaymentController::class, 'getAll']);
	$route->get('/payments/{id}', [PaymentController::class, 'getById']);
	$route->post('/payments/create', [PaymentController::class, 'create']);
	$route->put('/payments/update/{id}', [PaymentController::class, 'update']);
	$route->delete('/payments/delete/{id}', [PaymentController::class, 'delete']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/reviews', [ReviewController::class, 'getAll']);
	$route->get('/reviews/{id}', [ReviewController::class, 'getById']);
	$route->get('/reviews/service/{serviceId}', [ReviewController::class, 'getByServiceId']);
	$route->get('/reviews/customer/{customerId}', [ReviewController::class, 'getByCustomerId']);
	$route->get('/reviews/status/{status}', [ReviewController::class, 'getByStatus']);
	$route->post('/reviews/create', [ReviewController::class, 'create']);
	$route->put('/reviews/update/{id}', [ReviewController::class, 'update']);
	$route->delete('/reviews/delete/{id}', [ReviewController::class, 'delete']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	// Promosyon rotaları
	$route->get('/promotions', [PromotionController::class, 'getAll']);
	$route->get('/promotions/{id}', [PromotionController::class, 'getById']);
	$route->get('/promotions/code/{code}', [PromotionController::class, 'getByCode']);
	$route->get('/promotions/active', [PromotionController::class, 'getActive']);
	$route->get('/promotions/store/{storeId}', [PromotionController::class, 'getByStoreId']);
	$route->get('/promotions/service/{serviceId}', [PromotionController::class, 'getByServiceId']);
	$route->post('/promotions/create', [PromotionController::class, 'create']);
	$route->put('/promotions/update/{id}', [PromotionController::class, 'update']);
	$route->delete('/promotions/delete/{id}', [PromotionController::class, 'delete']);
	$route->patch('/promotions/status/{id}', [PromotionController::class, 'updateStatus']);

	// Kullanıcı promosyonları rotaları
	$route->get('/user-promotions', [UserPromotionController::class, 'getAll']);
	$route->get('/user-promotions/{id}', [UserPromotionController::class, 'getById']);
	$route->get('/user-promotions/user/{userId}', [UserPromotionController::class, 'getByUserId']);
	$route->post('/user-promotions/assign', [UserPromotionController::class, 'assign']);
	$route->put('/user-promotions/update/{id}', [UserPromotionController::class, 'update']);
	$route->delete('/user-promotions/delete/{id}', [UserPromotionController::class, 'delete']);
	$route->post('/user-promotions/increment-usage', [UserPromotionController::class, 'incrementUsage']);
	$route->post('/user-promotions/validate-code', [UserPromotionController::class, 'validateCode']);

	// Zamanlı promosyonlar rotaları
	$route->get('/timed-promotions', [TimedPromotionController::class, 'getAll']);
	$route->get('/timed-promotions/{id}', [TimedPromotionController::class, 'getById']);
	$route->get('/timed-promotions/promotion/{promotionId}', [TimedPromotionController::class, 'getByPromotionId']);
	$route->get('/timed-promotions/active', [TimedPromotionController::class, 'getActive']);
	$route->post('/timed-promotions/create', [TimedPromotionController::class, 'create']);
	$route->put('/timed-promotions/update/{id}', [TimedPromotionController::class, 'update']);
	$route->delete('/timed-promotions/delete/{id}', [TimedPromotionController::class, 'delete']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/agents/all', [AgentController::class, 'getAll']);
	$route->get('/agents/{id}', [AgentController::class, 'getById']);
	$route->post('/agents/create', [AgentController::class, 'create']);
	$route->put('/agents/update/{id}', [AgentController::class, 'update']);
	$route->delete('/agents/delete/{id}', [AgentController::class, 'delete']);
	$route->get('/agents/user/{userId}', [AgentController::class, 'getByUserId']);
	$route->get('/agents/{agentCode}', [AgentController::class, 'getByAgentCode']);
});

$route->prefix('api')->middleware(['Auth'])->group(function () use ($route) {
	$route->get('/agent-store-mappings', [AgentStoreMappingController::class, 'getAll']);
	$route->get('/agent-store-mappings/{id}', [AgentStoreMappingController::class, 'getById']);
	$route->post('/agent-store-mappings', [AgentStoreMappingController::class, 'create']);
	$route->put('/agent-store-mappings/{id}', [AgentStoreMappingController::class, 'update']);
	$route->delete('/agent-store-mappings/{id}', [AgentStoreMappingController::class, 'delete']);
});


