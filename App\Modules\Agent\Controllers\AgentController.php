<?php

declare(strict_types=1);

namespace App\Modules\Agent\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Agent\Services\AgentService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Agents", description="Satış Temsilcileri Yönetimi")
 */
class AgentController extends BaseController {
    public function __construct(
        Request $request,
        Response $response,
        Auth $auth,
        CheckRole $checkRole,
        private AgentService $agentService
    ) {
        parent::__construct($request, $response, $auth, $checkRole);
    }

    /**
     * @OA\Get(
     *     path="/api/agents/all",
     *     summary="Tüm satış temsilcilerini listeler",
     *     tags={"Agents"},
     *     @OA\Response(response="200", description="Başarılı"),
     *     @OA\Response(response="401", description="Yet<PERSON><PERSON> erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function getAll() {
        $agents = $this->agentService->getAll();
        return $this->success($agents);
    }

    /**
     * @OA\Get(
     *     path="/api/agents/{id}",
     *     summary="ID ile satış temsilcisi görüntüler",
     *     tags={"Agents"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Satış temsilcisi ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response="200", description="Başarılı"),
     *     @OA\Response(response="404", description="Satış temsilcisi bulunamadı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function getById(int $id) {
        $agent = $this->agentService->getById($id);
        return $this->success($agent);
    }

    /**
     * @OA\Post(
     *     path="/api/agents/create",
     *     summary="Yeni satış temsilcisi ekler",
     *     tags={"Agents"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"user_id", "agent_code"},
     *             @OA\Property(property="user_id", type="integer", example=1),
     *             @OA\Property(property="agent_code", type="string", example="AGT001"),
     *             @OA\Property(property="upper_agent_id", type="integer", example=1),
     *             @OA\Property(property="commission_rate", type="number", format="float", example=15.00),
     *             @OA\Property(property="tier", type="string", example="bronze"),
     *             @OA\Property(property="is_active", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(response="201", description="Satış temsilcisi oluşturuldu"),
     *     @OA\Response(response="400", description="Geçersiz istek"),
     *     @OA\Response(response="409", description="Temsilci kodu zaten var"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function create() {
        $data = $this->request->json();
        $agent = $this->agentService->create($data);
        return $this->success($agent);
    }

    /**
     * @OA\Put(
     *     path="/api/agents/update/{id}",
     *     summary="Satış temsilcisi bilgilerini günceller",
     *     tags={"Agents"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Satış temsilcisi ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="user_id", type="integer", example=1),
     *             @OA\Property(property="agent_code", type="string", example="AGT001"),
     *             @OA\Property(property="upper_agent_id", type="integer", example=1),
     *             @OA\Property(property="commission_rate", type="number", format="float", example=15.00),
     *             @OA\Property(property="tier", type="string", example="bronze"),
     *             @OA\Property(property="is_active", type="integer", example=1)
     *         )
     *     ),
     *     @OA\Response(response="200", description="Satış temsilcisi güncellendi"),
     *     @OA\Response(response="400", description="Geçersiz istek"),
     *     @OA\Response(response="404", description="Satış temsilcisi bulunamadı"),
     *     @OA\Response(response="409", description="Temsilci kodu zaten var"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function update(int $id) {
        $data = $this->request->json();
        $agent = $this->agentService->update($id, $data);
        return $this->success($agent);
    }

    /**
     * @OA\Delete(
     *     path="/api/agents/delete/{id}",
     *     summary="Satış temsilcisini siler",
     *     tags={"Agents"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Satış temsilcisi ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response="200", description="Satış temsilcisi silindi"),
     *     @OA\Response(response="404", description="Satış temsilcisi bulunamadı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function delete(int $id) {
        $this->agentService->delete($id);
        return $this->success();
    }

    /**
     * @OA\Get(
     *     path="/api/agents/user/{userId}",
     *     summary="Kullanıcı ID'sine göre satış temsilcisi getirir",
     *     tags={"Agents"},
     *     @OA\Parameter(
     *         name="userId",
     *         in="path",
     *         required=true,
     *         description="Kullanıcı ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response="200", description="Başarılı"),
     *     @OA\Response(response="404", description="Satış temsilcisi bulunamadı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function getByUserId(int $userId) {
        $agent = $this->agentService->getByUserId($userId);
        if (!$agent) {
            return $this->notFound();
        }
        return $this->success($agent);
    }

    /**
     * @OA\Get(
     *     path="/api/agents/{agentCode}",
     *     summary="Temsilci koduna göre satış temsilcisi getirir",
     *     tags={"Agents"},
     *     @OA\Parameter(
     *         name="agentCode",
     *         in="path",
     *         required=true,
     *         description="Temsilci kodu",
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\Response(response="200", description="Başarılı"),
     *     @OA\Response(response="404", description="Satış temsilcisi bulunamadı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function getByAgentCode(string $agentCode) {
        $agent = $this->agentService->getByAgentCode($agentCode);
        if (!$agent) {
            return $this->notFound();
        }
        return $this->success($agent);
    }
}
