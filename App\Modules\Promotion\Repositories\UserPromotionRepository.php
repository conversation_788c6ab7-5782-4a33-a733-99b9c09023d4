<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Promotion\Models\UserPromotion;
use System\Database\Database;
use System\Exception\SystemException;

class UserPromotionRepository extends BaseRepository {
   protected string $table = 'user_promotions';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu UserPromotion nesnesine dönüştürür.
    *
    * @param array $data
    * @return UserPromotion
    */
   protected function mapToObject(array $data): UserPromotion {
      return new UserPromotion(...$data);
   }

   /**
    * Kullanıcı promosyonu oluşturur.
    *
    * @param array $data
    * @return UserPromotion
    */
   public function create(array $data): UserPromotion {
      try {
         $stmt = $this->database->prepare(
            'INSERT INTO user_promotions (user_id, promotion_id, used_count)
             VALUES (:user_id, :promotion_id, :used_count)'
         );

         $stmt->execute([
            'user_id' => $data['user_id'],
            'promotion_id' => $data['promotion_id'],
            'used_count' => $data['used_count'] ?? 0
         ]);

         $id = $this->database->getLastId();
         return $this->getById((int)$id);
      } catch (\PDOException $e) {
         throw new SystemException('User promotion creation failed: ' . $e->getMessage());
      }
   }

   /**
    * Kullanıcı promosyonunu günceller.
    *
    * @param int $id
    * @param array $data
    * @return UserPromotion|null
    */
   public function update(int $id, array $data): ?object {
      try {
         $setFields = [];
         $params = ['id' => $id];

         if (isset($data['user_id'])) {
            $setFields[] = 'user_id = :user_id';
            $params['user_id'] = $data['user_id'];
         }

         if (isset($data['promotion_id'])) {
            $setFields[] = 'promotion_id = :promotion_id';
            $params['promotion_id'] = $data['promotion_id'];
         }

         if (isset($data['used_count'])) {
            $setFields[] = 'used_count = :used_count';
            $params['used_count'] = $data['used_count'];
         }

         if (isset($data['last_used_at'])) {
            $setFields[] = 'last_used_at = :last_used_at';
            $params['last_used_at'] = $data['last_used_at'];
         }

         if (empty($setFields)) {
            return null;
         }

         $sql = 'UPDATE user_promotions SET ' . implode(', ', $setFields) . ' WHERE id = :id';
         $stmt = $this->database->prepare($sql);
         $stmt->execute($params);

         return $stmt->getAffectedRows() > 0 ? $this->getById($id) : null;
      } catch (\PDOException $e) {
         throw new SystemException('User promotion update failed: ' . $e->getMessage());
      }
   }

   /**
    * Kullanıcı ID'sine göre promosyonları getirir.
    *
    * @param int $userId
    * @return array
    */
   public function getByUserId(int $userId): array {
      $stmt = $this->database->prepare(
         'SELECT up.*, p.code, p.type, p.value, p.start_date, p.end_date, p.status
          FROM user_promotions up
          JOIN promotions p ON up.promotion_id = p.id
          WHERE up.user_id = :user_id'
      );
      $stmt->execute(['user_id' => $userId]);
      $userPromotions = $stmt->getAll();

      return array_map(function ($userPromotion) {
         return $this->mapToObject((array)$userPromotion);
      }, $userPromotions);
   }

   /**
    * Promosyon ID'sine göre kullanıcı promosyonlarını getirir.
    *
    * @param int $promotionId
    * @return array
    */
   public function getByPromotionId(int $promotionId): array {
      $stmt = $this->database->prepare(
         'SELECT * FROM user_promotions WHERE promotion_id = :promotion_id'
      );
      $stmt->execute(['promotion_id' => $promotionId]);
      $userPromotions = $stmt->getAll();

      return array_map(function ($userPromotion) {
         return $this->mapToObject((array)$userPromotion);
      }, $userPromotions);
   }

   /**
    * Kullanıcı ve promosyon ID'sine göre kullanıcı promosyonunu getirir.
    *
    * @param int $userId
    * @param int $promotionId
    * @return UserPromotion|null
    */
   public function getByUserAndPromotionId(int $userId, int $promotionId): ?UserPromotion {
      $stmt = $this->database->prepare(
         'SELECT * FROM user_promotions
          WHERE user_id = :user_id AND promotion_id = :promotion_id'
      );
      $stmt->execute([
         'user_id' => $userId,
         'promotion_id' => $promotionId
      ]);
      $userPromotion = $stmt->getRow();

      if (!$userPromotion) {
         return null;
      }

      return $this->mapToObject((array)$userPromotion);
   }

   /**
    * Kullanıcı promosyonunun kullanım sayısını artırır.
    *
    * @param int $userId
    * @param int $promotionId
    * @return bool
    */
   public function incrementUsage(int $userId, int $promotionId): bool {
      try {
         $stmt = $this->database->prepare(
            'UPDATE user_promotions
             SET used_count = used_count + 1, last_used_at = NOW()
             WHERE user_id = :user_id AND promotion_id = :promotion_id'
         );
         $stmt->execute([
            'user_id' => $userId,
            'promotion_id' => $promotionId
         ]);

         return $stmt->getAffectedRows() > 0;
      } catch (\PDOException $e) {
         throw new SystemException('Failed to increment usage: ' . $e->getMessage());
      }
   }
}
