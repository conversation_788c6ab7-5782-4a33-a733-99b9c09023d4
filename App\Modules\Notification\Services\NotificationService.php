<?php

declare(strict_types=1);

namespace App\Modules\Notification\Services;

use App\Modules\Notification\Repositories\NotificationRepository;
use System\Exception\SystemException;
use System\Mail\Mail;

class NotificationService {
   public function __construct(
      private NotificationRepository $notificationRepository,
      private Mail $mail
   ) {
   }

   /**
    * Tüm bildirimleri getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $notifications = $this->notificationRepository->getAll();
      return array_map(function ($notification) {
         return $notification->jsonSerialize();
      }, $notifications);
   }

   /**
    * Rezervasyon ID'sine göre bildirimleri getirir.
    *
    * @param int $bookingId
    * @return array
    */
   public function getByBookingId(int $bookingId): array {
      $notifications = $this->notificationRepository->getByBookingId($bookingId);
      return array_map(function ($notification) {
         return $notification->jsonSerialize();
      }, $notifications);
   }

   /**
    * <PERSON>llanıcı ID'sine göre bildirimleri getirir.
    *
    * @param int $userId
    * @return array
    */
   public function getByUserId(int $userId): array {
      $notifications = $this->notificationRepository->getByUserId($userId);
      return array_map(function ($notification) {
         return $notification->jsonSerialize();
      }, $notifications);
   }

   /**
    * Bildirim gönderir.
    *
    * @param int $bookingId
    * @param string $notifyType
    * @param string|null $customMessage
    * @return array
    */
   public function sendNotification(int $bookingId, string $notifyType, ?string $customMessage = null): array {
      // Rezervasyon bilgilerini kontrol et
      $bookingExists = $this->checkBookingExists($bookingId);
      if (!$bookingExists) {
         throw new SystemException('Booking not found');
      }

      // Bildirim türünü doğrula
      if (!in_array($notifyType, ['email', 'sms', 'push'])) {
         throw new SystemException('Invalid notification type');
      }

      // Bildirim gönderme işlemi
      $success = false;
      $status = 'failed';

      try {
         switch ($notifyType) {
            case 'email':
               $success = $this->sendEmailNotification($bookingId, $customMessage);
               break;
            case 'sms':
               $success = $this->sendSmsNotification($bookingId, $customMessage);
               break;
            case 'push':
               $success = $this->sendPushNotification($bookingId, $customMessage);
               break;
         }

         $status = $success ? 'sent' : 'failed';
      } catch (\Exception $e) {
         $status = 'failed';
      }

      // Bildirim kaydını oluştur
      $notification = $this->notificationRepository->create([
         'booking_id' => $bookingId,
         'notify_type' => $notifyType,
         'status' => $status
      ]);

      return $notification->jsonSerialize();
   }

   /**
    * Bildirimi yeniden gönderir.
    *
    * @param int $notificationId
    * @return array
    */
   public function resendNotification(int $notificationId): array {
      $notification = $this->notificationRepository->getById($notificationId);

      if (!$notification) {
         throw new SystemException('Notification not found');
      }

      return $this->sendNotification(
         $notification->getBookingId(),
         $notification->getNotifyType()
      );
   }

   /**
    * Rezervasyon varlığını kontrol eder.
    *
    * @param int $bookingId
    * @return bool
    */
   private function checkBookingExists(int $bookingId): bool {
      // Burada rezervasyon varlığını kontrol etmek için veritabanı sorgusu yapılabilir
      // Şimdilik basit bir kontrol yapıyoruz
      return $bookingId > 0;
   }

   /**
    * E-posta bildirimi gönderir.
    *
    * @param int $bookingId
    * @param string|null $customMessage
    * @return bool
    */
   private function sendEmailNotification(int $bookingId, ?string $customMessage = null): bool {
      // Burada rezervasyon ve kullanıcı bilgilerini alıp e-posta gönderme işlemi yapılabilir
      // Örnek olarak Mail sınıfını kullanıyoruz
      try {
         // Gerçek uygulamada burada rezervasyon ve kullanıcı bilgileri alınıp
         // dinamik içerikli e-posta gönderilecektir
         $subject = 'Rezervasyon Bildirimi';
         $message = $customMessage ?? 'Rezervasyonunuz ile ilgili bir bildirim bulunmaktadır.';
         $message .= "\n\nRezervasyon ID: {$bookingId}";

         // Mail gönderme işlemi burada yapılacak
         // $this->mail->send($to, $subject, $message);

         return true;
      } catch (\Exception $e) {
         return false;
      }
   }

   /**
    * SMS bildirimi gönderir.
    *
    * @param int $bookingId
    * @param string|null $customMessage
    * @return bool
    */
   private function sendSmsNotification(int $bookingId, ?string $customMessage = null): bool {
      // SMS gönderme işlemi burada yapılacak
      // Gerçek uygulamada bir SMS API'si kullanılabilir
      return true;
   }

   /**
    * Push bildirimi gönderir.
    *
    * @param int $bookingId
    * @param string|null $customMessage
    * @return bool
    */
   private function sendPushNotification(int $bookingId, ?string $customMessage = null): bool {
      // Push notification gönderme işlemi burada yapılacak
      // Gerçek uygulamada Firebase veya başka bir push notification servisi kullanılabilir
      return true;
   }
}
