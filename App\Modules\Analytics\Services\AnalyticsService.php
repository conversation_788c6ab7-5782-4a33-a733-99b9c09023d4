<?php

declare(strict_types=1);

namespace App\Modules\Analytics\Services;

use App\Modules\Analytics\Repositories\AnalyticsEventRepository;
use App\Modules\Analytics\Repositories\AnalyticsMetricRepository;
use App\Modules\Analytics\Repositories\UserActivityRepository;
use System\Exception\SystemException;
use System\Http\Request;

class AnalyticsService {
   public function __construct(
      private AnalyticsEventRepository $eventRepository,
      private AnalyticsMetricRepository $metricRepository,
      private UserActivityRepository $userActivityRepository,
      private Request $request
   ) {
   }

   /**
    * Analitik olay kaydeder.
    *
    * @param string $eventType
    * @param array $data
    * @return array
    */
   public function trackEvent(string $eventType, array $data = []): array {
      try {
         $eventData = [
            'event_type' => $eventType,
            'user_id' => $data['user_id'] ?? null,
            'entity_type' => $data['entity_type'] ?? null,
            'entity_id' => $data['entity_id'] ?? null,
            'metadata' => $data['metadata'] ?? null,
            'ip_address' => $data['ip_address'] ?? $this->request->ip(),
            'user_agent' => $data['user_agent'] ?? $this->request->server('HTTP_USER_AGENT')
         ];

         $event = $this->eventRepository->create($eventData);

         // Kullanıcı aktivitesi varsa güncelle
         if ($eventData['user_id']) {
            $this->trackUserActivity($eventData['user_id'], $eventType, $data['metadata'] ?? null);
         }

         return $event->jsonSerialize();
      } catch (\Exception $e) {
         throw new SystemException('Failed to track event: ' . $e->getMessage());
      }
   }

   /**
    * Kullanıcı aktivitesi kaydeder.
    *
    * @param int $userId
    * @param string $activityType
    * @param array|null $metadata
    * @return array
    */
   public function trackUserActivity(int $userId, string $activityType, ?array $metadata = null): array {
      try {
         $activityData = [
            'user_id' => $userId,
            'activity_type' => $activityType,
            'metadata' => $metadata
         ];

         $activity = $this->userActivityRepository->trackActivity($activityData);
         return $activity->jsonSerialize();
      } catch (\Exception $e) {
         throw new SystemException('Failed to track user activity: ' . $e->getMessage());
      }
   }

   /**
    * Metrik kaydeder veya günceller.
    *
    * @param string $metricName
    * @param float $value
    * @param array $options
    * @return array
    */
   public function recordMetric(string $metricName, float $value, array $options = []): array {
      try {
         $metricData = [
            'metric_name' => $metricName,
            'metric_value' => $value,
            'dimension' => $options['dimension'] ?? null,
            'dimension_value' => $options['dimension_value'] ?? null,
            'period_type' => $options['period_type'] ?? 'daily',
            'period_start' => $options['period_start'] ?? date('Y-m-d 00:00:00'),
            'period_end' => $options['period_end'] ?? date('Y-m-d 23:59:59')
         ];

         $metric = $this->metricRepository->createOrUpdate($metricData);
         return $metric->jsonSerialize();
      } catch (\Exception $e) {
         throw new SystemException('Failed to record metric: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir kullanıcının analitik olaylarını getirir.
    *
    * @param int $userId
    * @param array $filters
    * @return array
    */
   public function getUserEvents(int $userId, array $filters = []): array {
      try {
         $events = $this->eventRepository->getByUserId($userId, $filters);
         return array_map(function ($event) {
            return $event->jsonSerialize();
         }, $events);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get user events: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir kullanıcının aktivitelerini getirir.
    *
    * @param int $userId
    * @return array
    */
   public function getUserActivities(int $userId): array {
      try {
         $activities = $this->userActivityRepository->getUserActivities($userId);
         return array_map(function ($activity) {
            return $activity->jsonSerialize();
         }, $activities);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get user activities: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir metrik için trend verilerini getirir.
    *
    * @param string $metricName
    * @param array $options
    * @return array
    */
   public function getMetricTrend(string $metricName, array $options = []): array {
      try {
         $periodType = $options['period_type'] ?? 'daily';
         $startDate = $options['start_date'] ?? date('Y-m-d 00:00:00', strtotime('-30 days'));
         $endDate = $options['end_date'] ?? date('Y-m-d 23:59:59');
         $dimension = $options['dimension'] ?? null;
         $dimensionValue = $options['dimension_value'] ?? null;

         $metrics = $this->metricRepository->getTrend(
            $metricName,
            $periodType,
            $startDate,
            $endDate,
            $dimension,
            $dimensionValue
         );

         return array_map(function ($metric) {
            return $metric->jsonSerialize();
         }, $metrics);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get metric trend: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir metrik adına göre metrikleri getirir.
    *
    * @param string $metricName
    * @param array $filters
    * @return array
    */
   public function getMetricsByName(string $metricName, array $filters = []): array {
      try {
         $metrics = $this->metricRepository->getByMetricName($metricName, $filters);
         return array_map(function ($metric) {
            return $metric->jsonSerialize();
         }, $metrics);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get metrics by name: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir boyut ve değere göre metrikleri getirir.
    *
    * @param string $dimension
    * @param string $dimensionValue
    * @param array $filters
    * @return array
    */
   public function getMetricsByDimension(string $dimension, string $dimensionValue, array $filters = []): array {
      try {
         $metrics = $this->metricRepository->getByDimension($dimension, $dimensionValue, $filters);
         return array_map(function ($metric) {
            return $metric->jsonSerialize();
         }, $metrics);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get metrics by dimension: ' . $e->getMessage());
      }
   }

   /**
    * En aktif kullanıcıları getirir.
    *
    * @param string $activityType
    * @param int $limit
    * @return array
    */
   public function getMostActiveUsers(string $activityType, int $limit = 10): array {
      try {
         $users = $this->userActivityRepository->getMostActiveUsers($activityType, $limit);
         return array_map(function ($user) {
            return $user->jsonSerialize();
         }, $users);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get most active users: ' . $e->getMessage());
      }
   }

   /**
    * Son aktif olan kullanıcıları getirir.
    *
    * @param int $limit
    * @return array
    */
   public function getRecentlyActiveUsers(int $limit = 10): array {
      try {
         $users = $this->userActivityRepository->getRecentlyActiveUsers($limit);
         return array_map(function ($user) {
            return $user->jsonSerialize();
         }, $users);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get recently active users: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir zaman aralığında aktif olan kullanıcı sayısını getirir.
    *
    * @param string $startDate
    * @param string $endDate
    * @param string|null $activityType
    * @return int
    */
   public function countActiveUsers(string $startDate, string $endDate, ?string $activityType = null): int {
      try {
         return $this->userActivityRepository->countActiveUsersInPeriod($startDate, $endDate, $activityType);
      } catch (\Exception $e) {
         throw new SystemException('Failed to count active users: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir olay türüne göre olayları getirir.
    *
    * @param string $eventType
    * @param array $filters
    * @return array
    */
   public function getEventsByType(string $eventType, array $filters = []): array {
      try {
         $events = $this->eventRepository->getByEventType($eventType, $filters);
         return array_map(function ($event) {
            return $event->jsonSerialize();
         }, $events);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get events by type: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir varlık türü ve ID'sine göre olayları getirir.
    *
    * @param string $entityType
    * @param int $entityId
    * @param array $filters
    * @return array
    */
   public function getEventsByEntity(string $entityType, int $entityId, array $filters = []): array {
      try {
         $events = $this->eventRepository->getByEntity($entityType, $entityId, $filters);
         return array_map(function ($event) {
            return $event->jsonSerialize();
         }, $events);
      } catch (\Exception $e) {
         throw new SystemException('Failed to get events by entity: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir zaman aralığındaki olayları sayar.
    *
    * @param array $filters
    * @return int
    */
   public function countEvents(array $filters = []): int {
      try {
         return $this->eventRepository->countEvents($filters);
      } catch (\Exception $e) {
         throw new SystemException('Failed to count events: ' . $e->getMessage());
      }
   }
}
