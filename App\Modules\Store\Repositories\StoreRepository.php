<?php

declare(strict_types=1);

namespace App\Modules\Store\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Store\Models\Store;
use System\Database\Database;
use System\Exception\SystemException;

class StoreRepository extends BaseRepository {
   protected string $table = 'stores';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   // getall miras alınır.
   // delete miras alınır.
   // getById miras alınır.

   /**
    * Sahiplenilen mağazaları getirir.
    *
    * @param int $ownerId
    * @return array
    */
   public function findByOwner(int $ownerId): array {
      $stmt = $this->database->prepare(
         'SELECT *
          FROM stores WHERE owner_id = :owner_id'
      );

      $stmt->execute(['owner_id' => $ownerId]);
      $stores = $stmt->getAll();

      return array_map(function ($store) {
         return $this->mapToObject((array)$store);
      }, $stores);
   }

   public function create(array $data): Store {
      $stmt = $this->database->prepare(
         'INSERT INTO stores (owner_id, name, phone, address, neighborhood, district, city, country, zip_code, link, type, status)
          VALUES (:owner_id, :name, :phone, :address, :neighborhood, :district, :city, :country, :zip_code, :link, :type, :status)'
      );
      $stmt->execute([
         'owner_id' => $data['owner_id'],
         'name' => $data['name'],
         'phone' => $data['phone'],
         'address' => $data['address'],
         'neighborhood' => $data['neighborhood'],
         'district' => $data['district'],
         'city' => $data['city'],
         'country' => $data['country'],
         'zip_code' => $data['zip_code'],
         'link' => $data['link'],
         'type' => $data['type'],
         'status' => $data['status']
      ]);
      return $this->getById((int)$this->database->getLastId());
   }

   public function update(int $storeId, array $data): ?object {
      try {
         $stmt = $this->database->prepare(
            'UPDATE stores SET
            owner_id = :owner_id,
            name = :name,
            phone = :phone,
            address = :address,
            neighborhood = :neighborhood,
            district = :district,
            city = :city,
            country = :country,
            zip_code = :zip_code,
            link = :link,
            type = :type,
            status = :status
         WHERE id = :id'
         );
         $stmt->execute([
            'owner_id' => $data['owner_id'],
            'name' => $data['name'],
            'phone' => $data['phone'],
            'address' => $data['address'],
            'neighborhood' => $data['neighborhood'],
            'district' => $data['district'],
            'city' => $data['city'],
            'country' => $data['country'],
            'zip_code' => $data['zip_code'],
            'link' => $data['link'],
            'type' => $data['type'],
            'status' => $data['status'],
            'id' => $storeId
         ]);
         return $stmt->getAffectedRows() > 0 ? $this->getById($storeId) : null;
      } catch (\PDOException) {
         throw new SystemException('Store update failed');
      }
   }

   public function findByName(string $name): ?object {
      $stmt = $this->database->prepare(
         'SELECT * FROM stores WHERE name = :name'
      );
      $stmt->execute(['name' => $name]);
      $store = $stmt->getRow();

      if (!$store) {
         return null;
      }

      return $this->mapToObject((array)$store);
   }
   public function findByPhone(string $phone): ?object {
      $stmt = $this->database->prepare(
         'SELECT * FROM stores WHERE phone = :phone'
      );
      $stmt->execute(['phone' => $phone]);
      $store = $stmt->getRow();

      if (!$store) {
         return null;
      }

      return $this->mapToObject((array)$store);
   }
   public function findByOwnerId(int $ownerId): ?object {
      $stmt = $this->database->prepare(
         'SELECT * FROM stores WHERE owner_id = :owner_id'
      );
      $stmt->execute(['owner_id' => $ownerId]);
      $store = $stmt->getRow();

      if (!$store) {
         return null;
      }

      return $this->mapToObject((array)$store);
   }

   public function findByLink(string $link): ?object {
      $stmt = $this->database->prepare(
         'SELECT * FROM stores WHERE link = :link'
      );
      $stmt->execute(['link' => $link]);
      $store = $stmt->getRow();

      if (!$store) {
         return null;
      }

      return $this->mapToObject((array)$store);
   }


   public function getTableName(): string {
      return $this->table;
   }

   public function mapToObject(array $data): object {
      return new Store(...(array)$data);
   }
}
