<?php

declare(strict_types=1);

namespace App\Core\Middlewares;

use System\Http\Request;
use System\Http\Response;
use App\Core\Cache\CacheManager;

class RateLimiter {
    private CacheManager $cache;
    private int $maxRequests = 60;
    private int $decayMinutes = 1;

    public function __construct(
        private Request $request,
        private Response $response,
        CacheManager $cache
    ) {
        $this->cache = $cache;
    }

    public function handle(callable $next): mixed {
        $key = $this->resolveRequestSignature($this->request);

        if ($this->tooManyAttempts($key)) {
            return $this->buildResponse($this->response, $key);
        }

        $this->incrementAttempts($key);
        return $next();
    }

    private function resolveRequestSignature(Request $request): string {
        $ip = $request->ip();
        $route = $request->uri();

        return md5($ip . '|' . $route);
    }

    private function tooManyAttempts(string $key): bool {
        $attempts = $this->attempts($key);

        return $attempts >= $this->maxRequests;
    }

    private function attempts(string $key): int {
        return (int) $this->cache->get($key) ?? 0;
    }

    private function incrementAttempts(string $key): int {
        $attempts = $this->attempts($key) + 1;

        $this->cache->set($key, $attempts, $this->decayMinutes * 60);

        return $attempts;
    }

    private function buildResponse(Response $response, string $key): Response {
        $response->status(429);
        $response->json('Çok fazla istek gönderildi. Lütfen daha sonra tekrar deneyin.'
        );

        return $response;
    }
}
