<?php

declare(strict_types=1);

namespace App\Modules\User\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\AuditLog\Models\AuditLogModel;
use App\Modules\User\Enums\UserRoles;
use App\Modules\User\Repository\UserRepository;
use App\Modules\User\Service\AuthService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="User Auth", description="User Auth işlemleri")
 */
class AuthController extends BaseController {

   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private AuthService $authService,
      private AuditLogModel $auditLog,
      private UserRepository $userRepo,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Post(
    *     tags={"User Auth"},
    *     path="/api/login",
    *     summary="Kullanıcı Girişi",
    *     security={{"Bearer": {}}},
    * @OA\Response(response=200, description="Success"),
    * @OA\Response(response=401,description="Unauthorized"),
    * @OA\RequestBody(required=true,
    *    @OA\MediaType(mediaType="application/json",
    *    @OA\Schema(required={"email", "password"},
    *       @OA\Property(property="email", type="string", example="<EMAIL>"),
    *       @OA\Property(property="password", type="string", example="12345678")
    *    ))
    * ))
    */
   public function login() {

      $user = $this->authService->login($this->request->json('email'), $this->request->json('password'));

      $this->auditLog->logEvent(
         $user->getId(),
         'login_email',
         ['method' => 'email', 'status' => 'success']
      );

      $config = import_config('defines.jwt');
      return $this->success([
         'token' => $this->authService->generateToken($user),
         'expires_in' => $config['expire']
      ]);
   }


   /**
    * @OA\Get(
    *     path="/api/role/list",
    *     summary="Kullanıcı Rollerini Getir",
    *     tags={"User Auth"},
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Success"),
    *     @OA\Response(response=403, description="Unauthorized")
    * )
    */
   public function getRoles() {
      $this->hasRole('admin');

      $roles = $this->userRepo->getRoles();
      return $this->success(['roles' => $roles]);
   }

   /**
    * @OA\Get(
    *     path="/api/role/get/{user_id}",
    *     summary="Kullanıcı Rolü Getir",
    *     tags={"User Auth"},
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="user_id",
    *         in="path",
    *         required=true,
    *         description="Kullanıcının id'si",
    *         @OA\Schema(type="integer", example=1)
    *     ),
    *     @OA\Response(response=200, description="Success"),
    *     @OA\Response(response=404, description="User not found")
    * )
    */
   public function getRoleById(int $userId) {
      $this->hasRole('admin');

      $role = $this->userRepo->getRoleById($userId);
      return $this->success(['role' => $role]);
   }

   /**
    * @OA\Post(
    *     path="/api/role/set",
    *     summary="Kullanıcı Rol Atama",
    *     tags={"User Auth"},
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\MediaType(
    *             mediaType="application/json",
    *             @OA\Schema(
    *                 required={"user_id", "role"},
    *                 @OA\Property(property="user_id", type="integer", example=1),
    *                 @OA\Property(property="role", type="string", example="customer")
    *             )
    *         )
    *     ),
    *     @OA\Response(response=200, description="Success"),
    *     @OA\Response(response=404, description="Rol bulunamadı"),
    *     @OA\Response(response=400, description="Invalid role")
    * )
    */
   public function setRole() {
      $this->hasRole('admin');
      if (!UserRoles::tryFrom($this->request->json('role'))) {
         return $this->error('Invalid role');
      }

      if (!$this->request->json('user_id') || !$this->request->json('role')) {
         return $this->error('Invalid request');
      }

      $userId = $this->request->json('user_id');
      $role = $this->request->json('role');
      $this->userRepo->setRole((int) $userId, $role);
      return $this->success();
   }
}
