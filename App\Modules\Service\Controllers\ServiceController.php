<?php

declare(strict_types=1);

namespace App\Modules\Service\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Service\Services\ServiceService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Services", description="Hizmetler Yönetimi")
 */
class ServiceController extends BaseController {
   public function __construct(
      private ServiceService $service,
      Response $response,
      Request $request,
      Auth $auth,
      CheckRole $checkRole,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *    tags={"Services"},
    *    path="/api/services",
    *    summary="Tüm Hizmetleri Listele",
    *    security={{"Bearer": {}}},
    *    @OA\Response(response=200, description="Hizmet listesi"),
    *    @OA\Response(response=401, description="Unauthorized"),
    *)
    */
   public function getAll() {
      $services = $this->service->getAll();
      return $this->success($services);
   }

   /**
    * @OA\Get(
    *    tags={"Services"},
    *    path="/api/services/view/{id}",
    *    summary="Hizmet detaylarını getir",
    *    security={{"Bearer": {}}},
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Response(response=200, description="Hizmet detayları")
    *)
    */
   public function getById(int $id) {
      $service = $this->service->getById($id);
      if (!$service) {
         return $this->error('Service not found');
      }
      return $this->success($service);
   }

   /**
    * @OA\Post(
    *    tags={"Services"},
    *    path="/api/services/create",
    *    summary="Yeni Hizmet Kaydı",
    *    operationId="addService",
    *    security={{"Bearer": {}}},
    *    @OA\Response(
    *       response=200,
    *       description="Hizmet eklendi",
    *       @OA\JsonContent(
    *          @OA\Property(property="status", type="integer", example=200),
    *          @OA\Property(property="message", type="string", example="Success"),
    *          @OA\Property(
    *             property="data",
    *             type="object",
    *             @OA\Property(property="id", type="integer", example=123),
    *             @OA\Property(property="name", type="string", example="ServiceName")
    *          )
    *       )
    *    ),
    *    @OA\Response(response=400, description="Validasyon Hatası"),
    *    @OA\Response(response=409, description="Çakışma Hatası"),
    *    @OA\RequestBody(
    *       required=true,
    *       @OA\MediaType(
    *          mediaType="application/json",
    *          @OA\Schema(
    *             required={"category_id", "name", "description"},
    *             @OA\Property(property="category_id", type="integer", example=1),
    *             @OA\Property(property="name", type="string", example="ServiceName"),
    *             @OA\Property(property="description", type="string", example="Service description")
    *          )
    *       )
    *    )
    * )
    */
   public function create() {
      $data = $this->request->json();
      $result = $this->service->create($data);
      return $this->success($result);
   }

   /**
    * @OA\Patch(
    *    tags={"Services"},
    *    path="/api/services/update",
    *    summary="Hizmet Güncelle",
    *    operationId="updateService",
    *    security={{"Bearer": {}}},
    *    @OA\RequestBody(required=true,
    *       @OA\MediaType(mediaType="application/json",
    *       @OA\Schema(required={"id", "name", "description"},
    *          @OA\Property(property="id", type="integer", example=1),
    *          @OA\Property(property="name", type="string", example="ServiceName"),
    *          @OA\Property(property="description", type="string", example="Service description")))),
    *    @OA\Response(response=200, description="Hizmet güncellendi"),
    *    @OA\Response(response=422, description="Validation error")
    * )
    */
   public function update() {
      $data = $this->request->json();
      $this->service->update((int)$data['id'], $data);
      return $this->success();
   }

   /**
    * @OA\Delete(
    *    tags={"Services"},
    *    path="/api/services/delete/{id}",
    *    summary="Hizmet Sil",
    *    security={{"Bearer": {}}},
    *    @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *    @OA\Response(response=200, description="Hizmet silindi")
    * )
    */
   public function delete(int $id) {
      $this->service->delete($id);
      return $this->success();
   }
}
