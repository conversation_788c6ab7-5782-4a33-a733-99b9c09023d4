<?php

declare(strict_types=1);

namespace App\Modules\Agent\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Agent\Services\AgentStoreMappingService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="AgentStoreMappings", description="Temsilci-Mağaza Eşleştirmeleri Yönetimi")
 */
class AgentStoreMappingController extends BaseController {
    public function __construct(
        Request $request,
        Response $response,
        Auth $auth,
        CheckRole $checkRole,
        private AgentStoreMappingService $mappingService
    ) {
        parent::__construct($request, $response, $auth, $checkRole);
    }

    /**
     * @OA\Get(
     *     path="/api/agent-store-mappings",
     *     summary="Tüm temsilci-mağaza eşleştirmelerini listeler",
     *     tags={"AgentStoreMappings"},
     *     @OA\Response(response="200", description="Başarılı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function getAll() {
        $mappings = $this->mappingService->getAll();
        return $this->success($mappings);
    }

    /**
     * @OA\Get(
     *     path="/api/agent-store-mappings/{id}",
     *     summary="ID ile temsilci-mağaza eşleştirmesi görüntüler",
     *     tags={"AgentStoreMappings"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Eşleştirme ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response="200", description="Başarılı"),
     *     @OA\Response(response="404", description="Eşleştirme bulunamadı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function getById(int $id) {
        $mapping = $this->mappingService->getById($id);
        return $this->success($mapping);
    }

    /**
     * @OA\Post(
     *     path="/api/agent-store-mappings",
     *     summary="Yeni temsilci-mağaza eşleştirmesi ekler",
     *     tags={"AgentStoreMappings"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"agent_id", "store_id", "commission_amount"},
     *             @OA\Property(property="agent_id", type="integer", example=1),
     *             @OA\Property(property="store_id", type="integer", example=1),
     *             @OA\Property(property="commission_type", type="string", example="one_time"),
     *             @OA\Property(property="commission_amount", type="number", format="float", example=5.00)
     *         )
     *     ),
     *     @OA\Response(response="201", description="Eşleştirme oluşturuldu"),
     *     @OA\Response(response="400", description="Geçersiz istek"),
     *     @OA\Response(response="409", description="Eşleştirme zaten var"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function create() {
        $data = $this->request->json();
        $mapping = $this->mappingService->create($data);
        return $this->success($mapping);
    }

    /**
     * @OA\Put(
     *     path="/api/agent-store-mappings/{id}",
     *     summary="Temsilci-mağaza eşleştirmesini günceller",
     *     description="agent_id ve store_id alanları birlikte değiştirilebilir. Ancak bu durumda yeni eşleştirme zaten varsa hata döndürülür.",
     *     tags={"AgentStoreMappings"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Eşleştirme ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             @OA\Property(property="agent_id", type="integer", example=1),
     *             @OA\Property(property="store_id", type="integer", example=1),
     *             @OA\Property(property="commission_type", type="string", example="one_time"),
     *             @OA\Property(property="commission_amount", type="number", format="float", example=5.00)
     *         )
     *     ),
     *     @OA\Response(response="200", description="Eşleştirme güncellendi"),
     *     @OA\Response(response="400", description="Geçersiz istek"),
     *     @OA\Response(response="404", description="Eşleştirme bulunamadı"),
     *     @OA\Response(response="409", description="Eşleştirme zaten var"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function update(int $id) {
        $data = $this->request->json();
        $this->mappingService->update($id, $data);
        return $this->success();
    }

    /**
     * @OA\Delete(
     *     path="/api/agent-store-mappings/{id}",
     *     summary="Temsilci-mağaza eşleştirmesini siler",
     *     tags={"AgentStoreMappings"},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         required=true,
     *         description="Eşleştirme ID",
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(response="200", description="Eşleştirme silindi"),
     *     @OA\Response(response="404", description="Eşleştirme bulunamadı"),
     *     @OA\Response(response="401", description="Yetkisiz erişim"),
     *     security={{"Bearer": {}}}
     * )
     */
    public function delete(int $id) {
        $this->mappingService->delete($id);
        return $this->success();
    }
}
