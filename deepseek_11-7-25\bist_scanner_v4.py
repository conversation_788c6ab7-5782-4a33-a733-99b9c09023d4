# bist_scanner_v5.py

import pandas as pd
import numpy as np
import random
import time
import warnings
from tvDatafeed import TvDatafeed, Interval
from datetime import datetime
from scipy.signal import find_peaks
from config import TV_USERNAME, TV_PASSWORD

warnings.filterwarnings('ignore')

# ----------------------------
# 1. K<PERSON><PERSON><PERSON>GÜRASYON SINIFI
# ----------------------------
class Config:
    # Deneme için dü<PERSON>k değerler
    MIN_TOTAL_SCORE = 5
    MIN_AVG_VOLUME_TL = 1_000_000
    MIN_PRICE = 10.0
    MIN_RISK_REWARD = 1.3

    # Normal kullanım için önerilen de<PERSON>erler
    # MIN_TOTAL_SCORE = 50
    # MIN_AVG_VOLUME_TL = 25_000_000
    # MIN_PRICE = 10.0
    # MIN_RISK_REWARD = 1.5

    N_BARS = 500
    INTERVAL = Interval.in_daily
    BIST_SYMBOLS = [
        'AKBNK', 'ARCLK', 'ASELS', 'BIMAS', 'DOHOL', 'EKGYO', 'ENJSA', 'EREGL',
        'FROTO', 'GARAN', 'GUBRF', 'HALKB', 'HEKTS', 'ISCTR', 'KCHOL', 'KOZAA',
        'KOZAL', 'KRDMD', 'MGROS', 'ODAS', 'OYAKC', 'PETKM', 'PGSUS', 'SAHOL',
        'SASA', 'SISE', 'TAVHL', 'TCELL', 'THYAO', 'TUPRS', 'VESTL', 'YKBNK',
        'TOASO', 'ULKER', 'TKFEN', 'TTKOM'
    ]
    INDEX_SYMBOL = "XU100"

    # Skorlama Ağırlıkları
    SCORE_WEIGHTS = {
        'TREND': {'MA_20_ABOVE': 5, 'MA_50_ABOVE': 10, 'MA_CROSS_POSITIVE': 15},
        'MOMENTUM': {'RSI_OPTIMUM': 10, 'MACD_POSITIVE': 10, 'MACD_ACCELERATING': 5},
        'VOLUME': {'HIGH_VOLUME': 15, 'MEDIUM_VOLUME': 5},
        'VALUE': {'RS_POSITIVE': 20, 'POSITIVE_DIVERGENCE': 20},
        'PATTERN': {'BULLISH_ENGULFING': 15, 'HAMMER': 10, 'DOUBLE_BOTTOM': 15},
        'PENALTY': {'NEGATIVE_DIVERGENCE': -15, 'RSI_OVERBOUGHT': -10}
    }

# ----------------------------
# VERİ YÖNETİM MODÜLÜ (DAHA SAĞLAM)
# ----------------------------
class BISTDataFetcher:
    def __init__(self, config):
        self.config = config
        self.is_connected = False
        self.tv = None
        try:
            # YENİ: Sorun çıkaran 'chromedriver_path' parametresi kaldırıldı.
            self.tv = TvDatafeed(TV_USERNAME, TV_PASSWORD)
            self.is_connected = True # Nesne başarıyla oluşturulduysa, bağlantıya hazır kabul edelim.
            print("✅ TradingView nesnesi başarıyla oluşturuldu. Bağlantı, ilk veri çekiminde test edilecek.")
        except Exception as e:
            # Giriş sırasında oluşan beklenmedik hataları yakala
            print(f"❌ TradingView'e giriş sırasında kritik bir hata oluştu: {e}")
            print("   Lütfen kullanıcı adı/şifre ve 2FA ayarlarınızı kontrol edin.")
            # self.is_connected zaten False, bir şey yapmaya gerek yok.

    def fetch_stock_data(self, symbol, retries=3, delay=5):
        if not self.is_connected: return None
        for i in range(retries):
            try:
                df = self.tv.get_hist(
                    symbol=symbol, exchange='BIST',
                    interval=self.config.INTERVAL, n_bars=self.config.N_BARS
                )
                if df is None or df.empty:
                    raise ValueError("Veri boş döndü")

                df.reset_index(inplace=True)
                expected_cols = ['datetime', 'open', 'high', 'low', 'close', 'volume']
                rename_map = {col: col_new for col, col_new in zip(df.columns, expected_cols) if col_new in expected_cols}
                df.rename(columns=rename_map, inplace=True)

                if not all(col in df.columns for col in ['datetime', 'close']):
                     raise ValueError("Gerekli sütunlar (datetime, close) bulunamadı.")

                df.set_index('datetime', inplace=True)
                return df.dropna()
            except Exception as e:
                if i < retries - 1:
                    print(f"  ⚠️ Veri çekme hatası ({symbol}), {delay} saniye sonra tekrar denenecek... ({i+1}/{retries})")
                    time.sleep(delay)
                else:
                    print(f"  ❌ Veri çekme başarısız ({symbol}) - {retries} deneme sonrası. Hata: {e}")
                    return None

    def fetch_index_data(self):
        if not self.is_connected: return None
        index_df = self.fetch_stock_data(self.config.INDEX_SYMBOL)
        return index_df['close'] if index_df is not None and not index_df.empty else None

# ...
class CandlestickPatterns:
    def find_patterns(self, df):
        patterns = {}
        if len(df) < 2: return patterns
        last, prev = df.iloc[-1], df.iloc[-2]
        if (prev['close'] < prev['open'] and last['close'] > last['open'] and
            last['close'] > prev['open'] and last['open'] < prev['close']):
            patterns['Bullish_Engulfing'] = True
        body, lower_wick = abs(last['close'] - last['open']), last['open'] - last['low']
        is_hammer = (lower_wick > body * 2) and (last['high'] - last['close'] < body)
        is_after_downtrend = df['close'].tail(5).iloc[0] > last['close']
        if is_hammer and is_after_downtrend: patterns['Hammer'] = True
        return patterns

class PatternDetector: # Orijinal kodunuzdaki formasyonlar
    def detect_double_bottom(self, df):
        if len(df) < 30: return False
        lows = df['low'].values[-30:]
        valleys, _ = find_peaks(-lows, distance=5, prominence=np.std(lows) * 0.3)
        if len(valleys) < 2: return False
        v1, v2 = lows[valleys[-2]], lows[valleys[-1]]
        return abs(v1 - v2) / max(v1, v2) < 0.03

class AdvancedAnalytics:
    def __init__(self, config):
        self.config = config
        self.candlestick_detector = CandlestickPatterns()
        self.pattern_detector = PatternDetector()


    def calculate_technical_indicators(self, df, index_close=None):
        # YENİ: Güvenlik Önlemi - Hesaplamalardan önce veri tiplerini garanti altına al
        # Bu adım, 'Cannot set a DataFrame' hatasını çözer.
        for col in ['open', 'high', 'low', 'close', 'volume']:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # Olası hatalı verileri (sayıya çevrilemeyen) temizle
        df.dropna(inplace=True)

        # ----- Mevcut hesaplamalar buradan sonra devam eder -----

        df['MA_20'] = df['close'].rolling(20).mean()
        df['MA_50'] = df['close'].rolling(50).mean()
        df['EMA_12'] = df['close'].ewm(span=12).mean()
        df['EMA_26'] = df['close'].ewm(span=26).mean()

        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0).ewm(alpha=1/14, adjust=False).mean()
        loss = -delta.where(delta < 0, 0).ewm(alpha=1/14, adjust=False).mean()

        # RSI hesaplamasında sıfıra bölme hatasını engelle
        rs = gain / loss
        rs.replace([np.inf, -np.inf], np.nan, inplace=True)
        rs.fillna(method='ffill', inplace=True)

        df['RSI'] = 100 - (100 / (1 + rs))
        df['RSI'].fillna(100, inplace=True) # Eğer hala NaN varsa, aşırı alım olarak kabul et

        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']

        # Hatanın olduğu yer burasıydı, artık veri tipi doğru olduğu için sorun çıkmayacak.
        df['Volume_MA'] = df['volume'].rolling(20).mean()
        df['Volume_Ratio'] = df['volume'] / df['Volume_MA']
        df['Avg_Volume_TL'] = (df['volume'] * df['close']).rolling(20).mean()

        if index_close is not None:
            # Endeks verisini hisse senedi DataFrame'inin tarih indeksiyle hizala
            aligned_index = index_close.reindex(df.index, method='ffill')
            df['RS_Value'] = (df['close'] / aligned_index)
            df['RS_MA50'] = df['RS_Value'].rolling(50).mean()

        return df.dropna()

    def detect_rsi_divergence(self, df, lookback=30):
        recent_df = df.tail(lookback)
        price_valleys, _ = find_peaks(-recent_df['close'], distance=5)
        rsi_valleys, _ = find_peaks(-recent_df['RSI'], distance=5)
        if len(price_valleys) >= 2 and len(rsi_valleys) >= 2:
            if (recent_df['close'].iloc[price_valleys[-1]] < recent_df['close'].iloc[price_valleys[-2]] and
                recent_df['RSI'].iloc[rsi_valleys[-1]] > recent_df['RSI'].iloc[rsi_valleys[-2]]):
                return 'Positive'
        price_peaks, _ = find_peaks(recent_df['close'], distance=5)
        rsi_peaks, _ = find_peaks(recent_df['RSI'], distance=5)
        if len(price_peaks) >= 2 and len(rsi_peaks) >= 2:
            if (recent_df['close'].iloc[price_peaks[-1]] > recent_df['close'].iloc[price_peaks[-2]] and
                recent_df['RSI'].iloc[rsi_peaks[-1]] < recent_df['RSI'].iloc[rsi_peaks[-2]]):
                return 'Negative'
        return None

    def get_analysis(self, df):
        scores = {'TREND': 0, 'MOMENTUM': 0, 'VOLUME': 0, 'VALUE': 0, 'PATTERN': 0}
        signals, alerts = [], []
        latest = df.iloc[-1]
        w = self.config.SCORE_WEIGHTS

        if latest['close'] > latest['MA_20']: scores['TREND'] += w['TREND']['MA_20_ABOVE']; signals.append("Fiyat > MA20")
        if latest['close'] > latest['MA_50']: scores['TREND'] += w['TREND']['MA_50_ABOVE']; signals.append("Fiyat > MA50")
        if latest['MA_20'] > latest['MA_50']: scores['TREND'] += w['TREND']['MA_CROSS_POSITIVE']; signals.append("MA20 > MA50")
        if 50 < latest['RSI'] < 70: scores['MOMENTUM'] += w['MOMENTUM']['RSI_OPTIMUM']; signals.append("RSI Güçlü (50-70)")
        if latest['RSI'] > 75: scores['MOMENTUM'] += w['PENALTY']['RSI_OVERBOUGHT']; alerts.append("RSI Aşırı Alım")
        if latest['MACD'] > latest['MACD_Signal']: scores['MOMENTUM'] += w['MOMENTUM']['MACD_POSITIVE']; signals.append("MACD Al Sinyali")
        if latest['Volume_Ratio'] > 2.0: scores['VOLUME'] += w['VOLUME']['HIGH_VOLUME']; signals.append("Hacim Patlaması (2x)")
        elif latest['Volume_Ratio'] > 1.3: scores['VOLUME'] += w['VOLUME']['MEDIUM_VOLUME']; signals.append("Hacim Ortalama Üstü")
        if 'RS_Value' in latest and latest['RS_Value'] > latest['RS_MA50']: scores['VALUE'] += w['VALUE']['RS_POSITIVE']; signals.append("RS Pozitif (Endeksten Güçlü)")
        divergence = self.detect_rsi_divergence(df)
        if divergence == 'Positive': scores['VALUE'] += w['VALUE']['POSITIVE_DIVERGENCE']; signals.append("Pozitif Uyuşmazlık")
        elif divergence == 'Negative': scores['VALUE'] += w['PENALTY']['NEGATIVE_DIVERGENCE']; alerts.append("Negatif Uyuşmazlık Riski")
        candle_patterns = self.candlestick_detector.find_patterns(df)
        if candle_patterns.get('Bullish_Engulfing'): scores['PATTERN'] += w['PATTERN']['BULLISH_ENGULFING']; signals.append("Mum: Yutan Boğa")
        if candle_patterns.get('Hammer'): scores['PATTERN'] += w['PATTERN']['HAMMER']; signals.append("Mum: Çekiç")
        if self.pattern_detector.detect_double_bottom(df): scores['PATTERN'] += w['PATTERN']['DOUBLE_BOTTOM']; signals.append("Formasyon: Çift Dip")

        total_score = sum(scores.values())
        return {'scores': scores, 'total_score': total_score, 'signals': signals, 'alerts': alerts}

class RiskManager:
    def calculate_targets_and_stops(self, df):
        current_price = df['close'].iloc[-1]
        try:
             atr_val = (df['high'] - df['low']).rolling(14).mean().iloc[-1]
        except (ValueError, IndexError):
             atr_val = current_price * 0.05
        stop_loss = current_price - (2 * atr_val)
        target_1 = current_price + (2.5 * atr_val)
        risk_reward = (target_1 - current_price) / (current_price - stop_loss) if stop_loss < current_price else 0
        return {'target_1': target_1, 'stop_loss': stop_loss, 'risk_reward': risk_reward}

class ReportGenerator:
    @staticmethod
    def generate_report(signals, filename="bist_analiz_raporu_v5.html"):
        report = """
        <!DOCTYPE html><html><head><title>BIST Analiz Raporu</title><meta charset="UTF-8">
        <style>
            body { font-family: 'Segoe UI', sans-serif; margin: 20px; background-color: #f4f7f6; color: #333; }
            .header { text-align: center; margin-bottom: 30px; }
            .header h1 { color: #2c3e50; } .header p { color: #7f8c8d; }
            .card { background: white; border-radius: 8px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); margin-bottom: 20px; overflow: hidden; }
            .card-header { padding: 20px; border-bottom: 1px solid #eee; display: flex; justify-content: space-between; align-items: center; }
            .card-header h2 { margin: 0; font-size: 1.5em; color: #3498db; }
            .total-score { font-size: 1.8em; font-weight: bold; color: #2ecc71; }
            .card-body { padding: 20px; display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
            .section h3 { border-bottom: 2px solid #3498db; padding-bottom: 5px; margin-top: 0; }
            .scores-grid { display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; }
            .score-item { background: #ecf0f1; padding: 10px; border-radius: 5px; }
            .score-item strong { color: #2c3e50; }
            .signals-list, .alerts-list { list-style: none; padding: 0; }
            .signals-list li { background: #e8f6f3; color: #16a085; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
            .alerts-list li { background: #fdedec; color: #c0392b; padding: 8px; margin-bottom: 5px; border-radius: 4px; }
        </style></head><body>
        <div class="header"><h1>🎯 BIST Profesyonel Analiz Raporu v5</h1><p>Oluşturulma: """ + datetime.now().strftime("%d/%m/%Y %H:%M") + f"</p></div>"

        for s in signals:
            scores_html = "".join([f"<div class='score-item'><strong>{k}:</strong> {v}</div>" for k, v in s['scores'].items() if v != 0])
            signals_html = "".join([f"<li>{sig}</li>" for sig in s['signals']])
            alerts_html = "".join([f"<li>{alt}</li>" for alt in s['alerts']])

            report += f"""
            <div class="card">
                <div class="card-header">
                    <h2>{s['symbol']} <small>({s['price']:.2f} ₺)</small></h2>
                    <div class="total-score">{s['total_score']}</div>
                </div>
                <div class="card-body">
                    <div class="section"><h3>Skor Dağılımı</h3><div class="scores-grid">{scores_html}</div></div>
                    <div class="section"><h3>Risk & Getiri</h3><div class="scores-grid">
                        <div class="score-item"><strong>Hedef 1:</strong> {s['target_1']:.2f} ₺</div>
                        <div class="score-item"><strong>Stop:</strong> {s['stop_loss']:.2f} ₺</div>
                        <div class="score-item"><strong>R/R:</strong> {s['risk_reward']:.2f}</div>
                    </div></div>
                    <div class="section"><h3>Pozitif Sinyaller</h3><ul class="signals-list">{signals_html}</ul></div>
                    <div class="section"><h3>Uyarılar</h3><ul class="alerts-list">{alerts_html if s['alerts'] else "<li>Yok</li>"}</ul></div>
                </div>
            </div>
            """
        report += "</body></html>"
        with open(filename, 'w', encoding='utf-8') as f: f.write(report)
        print(f"✅ Rapor oluşturuldu: {filename}")

# ----------------------------
# ANA İŞLEM SINIFI (DAHA "KONUŞKAN" VE SAĞLAM)
# ----------------------------
class BISTAnalyzer:
    def __init__(self, config):
        self.config = config
        self.fetcher = BISTDataFetcher(config)
        # Diğer sınıfların başlatılması aynı kalır...
        self.analytics = AdvancedAnalytics(config)
        self.risk_manager = RiskManager()
        self.report_generator = ReportGenerator()

    def analyze_symbol(self, symbol, index_data):
        # Bu fonksiyon aynı kalır...
        print(f"  Analiz ediliyor: {symbol}")
        df = self.fetcher.fetch_stock_data(symbol)
        if df is None or len(df) < 60:
            print(f"    => ❌ {symbol} filtrelendi. Sebep: Yetersiz veya hatalı veri.")
            return None

        df = self.analytics.calculate_technical_indicators(df, index_close=index_data)
        analysis_result = self.analytics.get_analysis(df)

        cfg = self.config
        latest = df.iloc[-1]
        risk_metrics = self.risk_manager.calculate_targets_and_stops(df)

        if analysis_result['total_score'] < cfg.MIN_TOTAL_SCORE:
            print(f"    => ❌ {symbol} filtrelendi. Sebep: Düşük Toplam Skor ({analysis_result['total_score']} < {cfg.MIN_TOTAL_SCORE})")
            return None
        if risk_metrics['risk_reward'] < cfg.MIN_RISK_REWARD:
            print(f"    => ❌ {symbol} filtrelendi. Sebep: Yetersiz R/R Oranı ({risk_metrics['risk_reward']:.2f} < {cfg.MIN_RISK_REWARD})")
            return None
        if latest['Avg_Volume_TL'] < cfg.MIN_AVG_VOLUME_TL:
            print(f"    => ❌ {symbol} filtrelendi. Sebep: Düşük Hacim ({latest['Avg_Volume_TL']/1e6:.1f}M < {cfg.MIN_AVG_VOLUME_TL/1e6:.0f}M TL)")
            return None
        if latest['close'] < cfg.MIN_PRICE:
            print(f"    => ❌ {symbol} filtrelendi. Sebep: Düşük Fiyat ({latest['close']:.2f} < {cfg.MIN_PRICE})")
            return None

        result = {
            'symbol': symbol,
            'price': latest['close'],
            **analysis_result,
            **risk_metrics
        }
        print(f"    => ✅ {symbol} için potansiyel sinyal bulundu! Toplam Skor: {result['total_score']}")
        return result


    def run_full_analysis(self):
        print("🚀 BIST Profesyonel Analiz Başlatılıyor...")
        print("="*50)

        if not self.fetcher.is_connected or self.fetcher.tv is None:
            print("❌ TradingView nesnesi oluşturulamadığı için analiz durduruluyor.")
            return

        print("ℹ️ Bağlantı, endeks verisi çekilerek test ediliyor...")
        index_data = self.fetcher.fetch_index_data()
        if index_data is None:
            print("❌ Kritik Hata: Endeks verisi (XU100) çekilemedi. Analiz durduruluyor.")
            return
        print("✅ Endeks verisi başarıyla çekildi. Analiz başlıyor.")

        all_signals = []

        # YENİ: Hata Ayıklama için try-except bloğunu geçici olarak kaldırdık.
        # Bu, programın çökmesine izin verecek ve bize tam hata raporunu gösterecek.
        for symbol in self.config.BIST_SYMBOLS:
            time.sleep(random.uniform(2.0, 4.0))

            # except bloğu kaldırıldığı için hatalar artık direkt konsola yazılacak.
            result = self.analyze_symbol(symbol, index_data)
            if result:
                all_signals.append(result)

        print("="*50)
        if not all_signals:
            print("ℹ️ Belirtilen kriterlere uygun sinyal bulunamadı.")
            return

        sorted_signals = sorted(all_signals, key=lambda x: x['total_score'], reverse=True)
        self.report_generator.generate_report(sorted_signals)
        print("\n🏆 EN GÜÇLÜ SİNYALLER:")
        for s in sorted_signals[:5]:
            print(f"  - {s['symbol']}: Skor={s['total_score']}, R/R={s['risk_reward']:.2f}, Öne Çıkan: {s['signals'][0] if s['signals'] else 'N/A'}")

# ----------------------------
# ANA ÇALIŞTIRMA
# ----------------------------
# if __name__ == "__main__":
#     try:
#         config = Config()
#         analyzer = BISTAnalyzer(config)
#         analyzer.run_full_analysis()
#     except KeyboardInterrupt:
#         print("\n⏹️ Kullanıcı tarafından durduruldu.")
#     except Exception as e:
#         print(f"\n❌ Ana programda beklenmedik bir hata oluştu: {e}")

if __name__ == "__main__":
    # YENİ: Hatanın tam yerini görebilmek için try...except bloğunu kaldırıyoruz.
    # Bu, programın çökmesine neden olacak, bu beklenen bir durumdur.
    config = Config()
    analyzer = BISTAnalyzer(config)
    analyzer.run_full_analysis()