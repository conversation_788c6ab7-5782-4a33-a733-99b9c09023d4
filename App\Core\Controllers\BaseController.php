<?php

declare(strict_types=1);

namespace App\Core\Controllers;

use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use System\Controller\Controller;
use System\Http\Request;
use System\Http\Response;

abstract class BaseController extends Controller {
   protected const HTTP_OK = 200;
   protected const HTTP_CREATED = 201;
   protected const HTTP_BAD_REQUEST = 400;
   protected const HTTP_UNAUTHORIZED = 401;
   protected const HTTP_FORBIDDEN = 403;
   protected const HTTP_NOT_FOUND = 404;

   protected function success(mixed $data = null, string $message = 'Success', int $code = self::HTTP_OK, array $meta = []) {
      return $this->jsonResponse($code, $message, $data, null, $meta);
   }

   protected function error(string $message, int $code = self::HTTP_BAD_REQUEST, mixed $errors = null, array $meta = []) {
      return $this->jsonResponse($code, $message, null, $errors, $meta);
   }

   protected function notFound(string $message = 'Resource not found') {
      return $this->jsonResponse(self::HTTP_NOT_FOUND, $message);
   }

   protected function forbidden(string $message = 'Forbidden') {
      return $this->jsonResponse(self::HTTP_FORBIDDEN, $message);
   }

   public function __construct(
      protected Request $request,
      protected Response $response,
      protected Auth $auth,
      protected CheckRole $checkRole
   ) {
   }

   /**
    * Check if the user has the required role(s)
    *
    * @param string|array $role
    * @return bool
    */
   protected function hasRole(string|array $role): bool {
      if (is_array($role)) {
         foreach ($role as $r) {
            if ($this->checkRole->handle($r)) {
               return true;
            }
         }
         return false;
      } else {
         return $this->checkRole->handle($role);
      }
   }

   /**
    * jsonResponse
    *
    * @param int $code
    * @param string $message
    * @param mixed|null $data
    * @param mixed|null $errors
    * @param array $meta
    *
    * @return void
    */
   protected function jsonResponse(int $code, string $message, mixed $data = null, mixed $errors = null, array $meta = []): void {
      $this->response->json($message, $data, $errors, $code, $meta);
   }
}
