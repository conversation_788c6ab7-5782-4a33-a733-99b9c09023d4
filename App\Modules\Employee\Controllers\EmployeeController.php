<?php

declare(strict_types=1);

namespace App\Modules\Employee\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Employee\Services\EmployeeService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Employee", description="Personel işlemleri")
 */
class EmployeeController extends BaseController {

   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private EmployeeService $employeeService
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *   tags={"Employee"},
    *   path="api/employees/all",
    *   summary="Tüm personelleri listele",
    *   security={{"Bearer": {}}},
    *   @OA\Response(response=200, description="Personel listesi"),
    *   @OA\Response(response=401, description="Unauthorized"),
    *   @OA\Response(response=500, description="Internal Server Error")
    * )
    */
   public function getAll() {

      $employees = $this->employeeService->getAll();
      return $this->success($employees);
   }

   /**
    * @OA\Get(
    *   tags={"Employee"},
    *   path="api/employees/{id}",
    *   summary="Personel detaylarını getir",
    *   security={{"Bearer": {}}},
    *   @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"), description="Personel ID"),
    *   @OA\Response(response=200, description="Personel detayları"),
    *   @OA\Response(response=404, description="Personel not found"),
    *   @OA\Response(response=401, description="Unauthorized")
    * )
    */
   public function getById(int $id) {

      $employee = $this->employeeService->getById($id);
      if (!$employee) {
         return $this->notFound();
      }
      return $this->success($employee->jsonSerialize());
   }

   /**
    * @OA\Post(
    *   tags={"Employee"},
    *   path="api/employees/create",
    *   summary="Yeni personel oluştur",
    *   security={{"Bearer": {}}},
    *   @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"), description="Personel ID"),
    *   @OA\Response(response=200, description="Personel detayları"),
    *   @OA\Response(response=404, description="Personel not found"),
    *   @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function create() {

      $data = $this->request->json();
      $employee = $this->employeeService->create($data);
      if (!$employee) {
         return $this->error('Failed to create employee.');
      }
      return $this->success($employee->jsonSerialize());
   }

   /**
    * @OA\Patch(
    *   tags={"Employee"},
    *   path="api/employees/update",
    *   summary="Personel güncelle",
    *   security={{"Bearer": {}}},
    *   @OA\RequestBody(required=true,
    *       @OA\MediaType(mediaType="application/json",
    *       @OA\Schema(required={"name", "email", "phone", "position"},
    *          @OA\Property(property="name", type="string", example="John Doe"),
    *          @OA\Property(property="email", type="string", example="<EMAIL>"),
    *          @OA\Property(property="phone", type="string", example="************"),
    *          @OA\Property(property="position", type="string", example="Manager")))),
    *   @OA\Response(response=200, description="Success"),
    *   @OA\Response(response=400, description="Bad Request"),
    *   @OA\Response(response=404, description="Not Found"),
    *   @OA\Response(response=422, description="Unprocessable Entity")
    * )
    */
   public function update() {

      $data = $this->request->json();
      $success = $this->employeeService->update(1, $data);
      if (!$success) {
         if (!$this->employeeService->getById(1)) {
            return $this->notFound();
         }
         return $this->error('Failed to update employee.');
      }
      $this->success();
   }

   /**
    * @OA\Delete(
    *   tags={"Employee"},
    *   path="api/employees/delete/{id}",
    *   summary="Personel sil",
    *   security={{"Bearer": {}}},
    *   @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer"), description="Personel ID"),
    *   @OA\Response(response=200, description="Personel silindi"),
    *   @OA\Response(response=404, description="Personel not found"),
    *   @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function delete(int $id) {

      $success = $this->employeeService->delete($id);
      if (!$success) {
         $existingEmployee = $this->employeeService->getById($id);
         if (!$existingEmployee) {
            return $this->notFound();
         }
         return $this->error('Failed to delete employee.');
      }
      $this->success();
   }

   /**
    * @OA\Get(
    *   tags={"Employee"},
    *   path="api/stores/{storeId}/employees",
    *   summary="Mağazaya ait personelleri listele",
    *   security={{"Bearer": {}}},
    *   @OA\Parameter(name="storeId", in="path", required=true, @OA\Schema(type="integer"), description="Mağaza ID"),
    *   @OA\Response(response=200, description="Personel listesi"),
    *   @OA\Response(response=404, description="Mağaza bulunamadı veya personel yok"),
    *   @OA\Response(response=401, description="Unauthorized"),
    *   @OA\Response(response=500, description="Internal Server Error")
    * )
    */

   public function getByStoreId(int $storeId) {
      $employees = $this->employeeService->getByStoreId($storeId);

      $employeeData = array_map(fn($emp) => $emp->jsonSerialize(), $employees);
      $this->success($employeeData);
   }
}
