<?php

declare(strict_types=1);

namespace App\Modules\Store\Services;

use App\Core\Validation\Validator;
use App\Modules\Store\Models\Store;
use App\Modules\Store\Repositories\StoreRepository;
use System\Exception\SystemException;
use System\Http\Request;

class StoreService {
   // Define validation rules based on Store model
   private array $validationRules = [
      'owner_id' => ['required', 'numeric'],
      'name' => ['required', 'max:100'],
      'phone' => ['required', 'max:20'],
      'address' => ['max:255'],
      'neighborhood' => ['max:100'],
      'district' => ['max:100'],
      'city' => ['required', 'max:100'],
      'country' => ['required', 'max:100'],
      'zip_code' => ['max:20'],
      'link' => ['required', 'max:100'], // Assuming link is required and unique
      'type' => ['required', 'max:50'], // e.g., 'barber', 'dentist'
      'status' => ['required', 'in:active,inactive,pending'] // Example statuses
   ];

   public function __construct(
      private StoreRepository $storeRepository,
      private Request $request,
      private Validator $validator // Inject Validator
   ) {
   }

   public function getAll(): array {
      $stores = $this->storeRepository->getAll();
      return array_map(function ($store) {
         return $store->jsonSerialize();
      }, $stores);
   }

   public function getById(int $id): ?object {
      return $this->storeRepository->getById($id);
   }

   public function getByOwner(int $userId): array {
      $stores = $this->storeRepository->findByOwner($userId);
      return array_map(function ($store) {
         return $store->jsonSerialize();
      }, $stores);
   }

   public function create(array $data): Store {
      // Add validation
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Check uniqueness *after* validation
      if ($this->storeRepository->findByName($data['name'])) {
         throw new SystemException('Store name already exists');
      }
      if ($this->storeRepository->findByPhone($data['phone'])) {
         throw new SystemException('Phone number already in use');
      }
      if ($this->storeRepository->findByLink($data['link'])) {
         throw new SystemException('Store link already in use');
      }
      return $this->storeRepository->create($data);
   }

   public function update(int $userId, array $data): ?object {
       // Ensure 'id' is present in data for fetching the store
       if (!isset($data['id'])) {
           throw new SystemException('Store ID is required for update.');
       }
       $storeId = (int)$data['id'];

       // Fetch the store first to check ownership and existence
       $store = $this->storeRepository->getById($storeId);
       if (!$store) {
           throw new SystemException('Store not found');
       }

       // Check ownership
       if ($store->getOwnerId() !== $userId) {
           throw new SystemException('Forbidden: You do not own this store.');
       }

       // Validate provided data
       $updateRules = array_intersect_key($this->validationRules, $data);
       // Remove owner_id from update rules as it shouldn't be changed here
       unset($updateRules['owner_id']);
       if (!$this->validator->validate($data, $updateRules)) {
           throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
       }

       // Check uniqueness *after* validation, only if the field is being updated
       if (isset($data['name'])) {
           $existingStore = $this->storeRepository->findByName($data['name']);
           if ($existingStore && $existingStore->getId() !== $storeId) {
               throw new SystemException('Store name already exists');
           }
       }
       if (isset($data['phone'])) {
            $existingStore = $this->storeRepository->findByPhone($data['phone']);
            if ($existingStore && $existingStore->getId() !== $storeId) {
                throw new SystemException('Phone number already in use');
            }
        }
       if (isset($data['link'])) {
            $existingStore = $this->storeRepository->findByLink($data['link']);
            if ($existingStore && $existingStore->getId() !== $storeId) {
                throw new SystemException('Store link already in use');
            }
        }

       // Perform the update
       return $this->storeRepository->update($storeId, $data);
   }

   public function delete(int $storeId): bool {
      if ($storeId === null) {
         throw new SystemException('Unauthorized');
      }

      $store = $this->storeRepository->getById($storeId);
      if (!$store) {
         throw new SystemException('Store not found');
      }

      return $this->storeRepository->delete($storeId);
   }
}
