<?php

declare(strict_types=1);

namespace App\Modules\Review\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Review\Services\ReviewService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Reviews", description="Yorum ve Değerlendirme İşlemleri")
 */
class ReviewController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private ReviewService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Reviews"},
    *     path="/api/reviews",
    *     summary="Tüm Yorumları Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Yorum listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      $reviews = $this->service->getAll();
      return $this->success($reviews);
   }

   /**
    * @OA\Get(
    *     tags={"Reviews"},
    *     path="/api/reviews/{id}",
    *     summary="ID ile Yorum Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Yorum ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Yorum detayı"),
    *     @OA\Response(response=404, description="Yorum bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getById(int $id) {
      $review = $this->service->getById($id);
      return $this->success($review);
   }

   /**
    * @OA\Get(
    *     tags={"Reviews"},
    *     path="/api/reviews/service/{serviceId}",
    *     summary="Hizmet ID'sine Göre Yorumları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="serviceId",
    *         in="path",
    *         required=true,
    *         description="Hizmet ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Yorum listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByServiceId(int $serviceId) {
      $reviews = $this->service->getByServiceId($serviceId);
      return $this->success($reviews);
   }

   /**
    * @OA\Get(
    *     tags={"Reviews"},
    *     path="/api/reviews/customer/{customerId}",
    *     summary="Müşteri ID'sine Göre Yorumları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="customerId",
    *         in="path",
    *         required=true,
    *         description="Müşteri ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Yorum listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByCustomerId(int $customerId) {
      $reviews = $this->service->getByCustomerId($customerId);
      return $this->success($reviews);
   }

   /**
    * @OA\Get(
    *     tags={"Reviews"},
    *     path="/api/reviews/status/{status}",
    *     summary="Duruma Göre Yorumları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="status",
    *         in="path",
    *         required=true,
    *         description="Yorum Durumu (active, passive, archived)",
    *         @OA\Schema(type="string", enum={"active", "passive", "archived"})
    *     ),
    *     @OA\Response(response=200, description="Yorum listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByStatus(string $status) {
      $reviews = $this->service->getByStatus($status);
      return $this->success($reviews);
   }

   /**
    * @OA\Post(
    *     tags={"Reviews"},
    *     path="/api/reviews/create",
    *     summary="Yeni Yorum Oluştur",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         description="Yorum verileri",
    *         @OA\JsonContent(
    *             required={"service_id", "customer_id", "rating"},
    *             @OA\Property(property="service_id", type="integer", example=1),
    *             @OA\Property(property="customer_id", type="integer", example=1),
    *             @OA\Property(property="rating", type="integer", example=5),
    *             @OA\Property(property="review", type="string", example="Harika bir hizmet!"),
    *             @OA\Property(property="status", type="string", example="active", enum={"active", "passive", "archived"})
    *         )
    *     ),
    *     @OA\Response(response=201, description="Yorum oluşturuldu"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function create() {
      $data = $this->request->json();
      $review = $this->service->create($data);
      return $this->success($review);
   }

   /**
    * @OA\Put(
    *     tags={"Reviews"},
    *     path="/api/reviews/update/{id}",
    *     summary="Yorum Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Yorum ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         description="Güncellenecek yorum verileri",
    *         @OA\JsonContent(
    *             @OA\Property(property="service_id", type="integer", example=1),
    *             @OA\Property(property="customer_id", type="integer", example=1),
    *             @OA\Property(property="rating", type="integer", example=4),
    *             @OA\Property(property="review", type="string", example="Güncellenen yorum"),
    *             @OA\Property(property="response", type="string", example="İşletme yanıtı"),
    *             @OA\Property(property="status", type="string", example="active", enum={"active", "passive", "archived"})
    *         )
    *     ),
    *     @OA\Response(response=200, description="Yorum güncellendi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Yorum bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function update(int $id) {
      $data = $this->request->json();
      $success = $this->service->update($id, $data);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to update review');
   }

   /**
    * @OA\Delete(
    *     tags={"Reviews"},
    *     path="/api/reviews/delete/{id}",
    *     summary="Yorum Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Yorum ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Yorum silindi"),
    *     @OA\Response(response=404, description="Yorum bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function delete(int $id) {
      $success = $this->service->delete($id);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to delete review');
   }
}
