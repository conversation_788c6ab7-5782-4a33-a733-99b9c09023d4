<?php

declare(strict_types=1);

namespace App\Core\Repositories;

use System\Database\Database;

/**
 * @template T of object
 */
abstract class BaseRepository implements IRepository {
   /**
    * @param Database $database
    */
   public function __construct(
      protected Database $database
   ) {
   }

   /**
    * Tüm kayıtları getirir.
    *
    * @return array<T>
    */
   public function getAll(): array {
      $tableName = $this->getTableName();

      $statement = $this->database->prepare(
         "SELECT * FROM {$tableName}"
      );
      $statement->execute();
      $data = $statement->fetchAll();

      return array_map(function ($row) {
         return $this->mapToObject((array)$row);
      }, $data);
   }

   /**
    * ID ile kayıt getirir.
    *
    * @param int $id
    * @return T|null
    */
   public function getById(int $id): ?object {
      $tableName = $this->getTableName();
      $data = $this->database->prepare(
         "SELECT * FROM {$tableName} WHERE id = :id"
      )->execute([
         'id' => $id
      ])->fetch();

      if (!$data) {
         return null;
      }

      return $this->mapToObject((array)$data);
   }

   /**
    * Kaydı siler.
    *
    * @param int $id
    * @return bool
    */
   public function delete(int $id): bool {
      $tableName = $this->getTableName();
      $result = $this->database->prepare(
         "DELETE FROM {$tableName} WHERE id = :id"
      )->execute([
         'id' => $id
      ]);

      return $result->getAffectedRows() > 0;
   }

   /**
    * Yeni kayıt oluşturur.
    *
    * @param array $data
    * @return object|null
    */
   public function create(array $data): ?object {
      // Bu metot alt sınıflarda uygulanmalıdır
      return null;
   }

   /**
    * Kaydı günceller.
    *
    * @param int $id
    * @param array $data
    * @return object|null
    */
   public function update(int $id, array $data): ?object {
      // Bu metot alt sınıflarda uygulanmalıdır
      return null;
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   abstract protected function getTableName(): string;

   /**
    * Veritabanı satırını nesneye dönüştürür.
    *
    * @param array $data
    * @return object
    */
   abstract protected function mapToObject(array $data): object;
}
