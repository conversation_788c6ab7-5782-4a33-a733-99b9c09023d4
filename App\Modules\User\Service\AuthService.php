<?php

declare(strict_types=1);

namespace App\Modules\User\Service;

use App\Modules\User\Models\User;
use App\Modules\User\Repository\UserRepository;
use System\Exception\SystemException;
use System\Secure\Hash;
use System\Jwt\Jwt;

class AuthService {
   private const LOGIN_ATTEMPT_DELAY = 1; //COMBAK -> süre ayarı yapılmalı. Bu sayede kullanıcı var/yok durumlarında aynı sürede yanıt alınır. Böylece saldırganlar geçerli e-postaları tespit edemez.

   public function __construct(
      private UserRepository $userRepo,
      private Hash $hash,
      private Jwt $jwt
   ) {
   }

   /**
    * Kullanıcı girişini gerçekleştirir.
    *
    * @param string $email
    * @param string $password
    * @return User
    * @throws SystemException
    */
   public function login(string $email, string $password): User {
      sleep(self::LOGIN_ATTEMPT_DELAY); // Timing attack önlemi

      $data = $this->sanitizeInput($email, $password);

      $user = $this->userRepo->findByEmail($data['email']);

      $this->validateCredentials($user, $data['password']);
      $this->userRepo->checkAccountLock($user);

      $this->userRepo->resetLoginAttempts($user->getEmail());
      $this->userRepo->updateLoginStats($user->getEmail());
      return $user;
   }

   private function sanitizeInput(string $email, string $password): array {
      $password = trim($password);
      $email = trim($email);

      return [
         'email' => filter_var($email ?? '', FILTER_SANITIZE_EMAIL),
         'password' => (string) ($password ?? '')
      ];
   }

   /**
    * @param User|null $user
    * @param string $password
    * @throws SystemException
    */
   private function validateCredentials(?User $user, string $password): void {
      $verify = $this->verifyPassword($password, $user?->getPassword());
      if ($verify) {
         $this->userRepo->resetLoginAttempts($user?->getEmail());
      }
      //TODO Burada bulunmayan mailleri kilitlendi göstermek için sanki varmış gibi temp bir sql tablosundan kayıt kontrolü yapılabilir.
      // Böylece var olan email tespiti zorlaşır
      if (!$user || !$verify) {
         $this->userRepo->incrementLoginAttempts($user?->getEmail()); // Kullanıcı yoksa null olur ve bu kontrolü geçer. Bu sayede kullanıcı var/yok durumlarında aynı sürede yanıt alınır. Böylece saldırganlar geçerli e-postaları tespit edemez.
         throw new SystemException('Geçersiz kimlik bilgileri');
      }
   }

   /**
    * Mantık: Kullanıcı var/yok durumlarında aynı sürede yanıt almayı sağlar. Böylece saldırganlar geçerli e-postaları tespit edemez.
    *
    * @param string $password
    * @param null|string $hash
    * @return bool
    */
   private function verifyPassword(string $password, ?string $hash): bool {
      $dummyHash = '$2y$10$dummyhashdummyhashdummyha'; // Fake bcrypt hash

      $realHash = $hash ?? $dummyHash;
      return $this->hash->verify($password, $realHash) && $hash !== null;
   }

   /**
    * Kullanıcıya ait bir JWT token oluşturur.
    *
    * @param User $user
    * @return string
    */
   public function generateToken(User $user): string {
      return $this->jwt->encode([
         'sub' => $user->getId(),
         'role' => $user->getRole()
      ]);
   }
}
