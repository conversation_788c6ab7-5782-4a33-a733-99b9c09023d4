<?php

declare(strict_types=1);

namespace App\Modules\Booking\Services;

use App\Core\Validation\Validator;
use App\Modules\Booking\Models\Booking;
use App\Modules\Booking\Repositories\BookingRepository;
use System\Exception\SystemException;
use System\Http\Request;

class BookingService {
   // Define validation rules based on Booking model
   private array $validationRules = [
      'customer_id' => ['required', 'numeric'],
      'store_id' => ['required', 'numeric'],
      'service_id' => ['required', 'numeric'],
      'employee_id' => ['required', 'numeric'],
      'start_time' => ['required'], // Add specific date/time validation if needed
      'end_time' => ['required'],   // Add specific date/time validation if needed
      'final_price' => ['numeric'],
      'note' => ['max:255'],
      'status' => ['required', 'in:pending,confirmed,cancelled,completed'] // Use 'in' rule if Validator supports it
   ];

   public function __construct(
      private BookingRepository $bookingRepository,
      private Request $request,
      private Validator $validator // Inject Validator
   ) {

   }

   /**
    * Tüm rezervasyonları getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $bookings = $this->bookingRepository->getAll();
      return array_map(function ($booking) {
         return $booking->jsonSerialize();
      }, $bookings);
   }

   /**
    * ID ile rezervasyon getirir.
    *
    * @param int $id
    * @return array
    */
   public function getById(int $id): array {
      $booking = $this->bookingRepository->getById($id);
      if (!$booking) {
         throw new SystemException('Booking not found');
      }
      return $booking->jsonSerialize();
   }

   /**
    * Müşteriye ait rezervasyonları getirir.
    *
    * @param int $customerId
    * @return array
    */
   public function getCustomerBookings(int $customerId): array {
      $bookings = $this->bookingRepository->findByCustomer($customerId);
      return array_map(function ($booking) {
         return $booking->jsonSerialize();
      }, $bookings);
   }

   /**
    * İşletmeye ait rezervasyonları getirir.
    *
    * @param int $storeId
    * @return array
    */
   public function getStoreBookings(int $storeId): array {
      $bookings = $this->bookingRepository->findByStore($storeId);
      return array_map(function ($booking) {
         return $booking->jsonSerialize();
      }, $bookings);
   }

   /**
    * Çalışana ait rezervasyonları getirir.
    *
    * @param int $employeeId
    * @return array
    */
   public function getEmployeeBookings(int $employeeId): array {
      $bookings = $this->bookingRepository->findByEmployee($employeeId);
      return array_map(function ($booking) {
         return $booking->jsonSerialize();
      }, $bookings);
   }

   /**
    * Belirli bir tarih aralığındaki rezervasyonları getirir.
    *
    * @param string $startDate
    * @param string $endDate
    * @return array
    */
   public function getBookingsByDateRange(string $startDate, string $endDate): array {
      $bookings = $this->bookingRepository->findByDateRange($startDate, $endDate);
      return array_map(function ($booking) {
         return $booking->jsonSerialize();
      }, $bookings);
   }

   /**
    * Belirli bir durumdaki rezervasyonları getirir.
    *
    * @param string $status
    * @return array
    */
   public function getBookingsByStatus(string $status): array {
      $bookings = $this->bookingRepository->findByStatus($status);
      return array_map(function ($booking) {
         return $booking->jsonSerialize();
      }, $bookings);
   }

   /**
    * Yeni rezervasyon oluşturur.
    *
    * @param array $data
    * @return Booking
    */
   public function createBooking(array $data): Booking {
      // Add validation
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Çakışan rezervasyon kontrolü
      if ($this->bookingRepository->hasConflictingBookings(
         (int)$data['employee_id'],
         $data['start_time'],
         $data['end_time']
      )) {
         throw new SystemException('Conflicting booking exists for this time slot');
      }

      return $this->bookingRepository->create($data);
   }

   /**
    * Rezervasyon günceller.
    *
    * @param int $bookingId
    * @param array $data
    * @return Booking|null
    */
   public function updateBooking(int $bookingId, array $data): ?Booking {
      // Add validation
      // Create rules dynamically for update, only validating provided fields
      $updateRules = array_intersect_key($this->validationRules, $data);
      if (!$this->validator->validate($data, $updateRules)) {
          throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      $booking = $this->bookingRepository->getById($bookingId);
      if (!$booking) {
         throw new SystemException('Booking not found');
      }

      // Çakışan rezervasyon kontrolü (kendi ID'si hariç)
      // Use validated data if possible, or ensure keys exist before accessing
      $employeeId = $data['employee_id'] ?? $booking->getEmployeeId();
      $startTime = $data['start_time'] ?? $booking->getStartTime();
      $endTime = $data['end_time'] ?? $booking->getEndTime();

      if ($employeeId && $startTime && $endTime) { // Check if values are set
          if ($this->bookingRepository->hasConflictingBookings(
              (int)$employeeId,
              $startTime,
              $endTime,
              $bookingId
          )) {
              throw new SystemException('Conflicting booking exists for this time slot');
          }
      }

      return $this->bookingRepository->update($bookingId, $data);
   }

   /**
    * Rezervasyon durumunu günceller.
    *
    * @param int $bookingId
    * @param string $status
    * @return bool
    */
   public function updateBookingStatus(int $bookingId, string $status): bool {
      $booking = $this->bookingRepository->getById($bookingId);
      if (!$booking) {
         throw new SystemException('Booking not found');
      }

      // Status değerinin geçerli olup olmadığını kontrol et
      $validStatuses = ['pending', 'confirmed', 'cancelled', 'completed'];
      if (!in_array($status, $validStatuses)) {
         throw new SystemException('Invalid status value');
      }

      return $this->bookingRepository->updateStatus($bookingId, $status);
   }

   /**
    * Rezervasyon siler.
    *
    * @param int $bookingId
    * @return bool
    */
   public function deleteBooking(int $bookingId): bool {
      $booking = $this->bookingRepository->getById($bookingId);
      if (!$booking) {
         throw new SystemException('Booking not found');
      }

      return $this->bookingRepository->delete($bookingId);
   }

   /**
    * Belirli bir zaman diliminde müsaitlik kontrolü yapar.
    *
    * @param int $employeeId
    * @param string $startTime
    * @param string $endTime
    * @return bool
    */
   public function checkAvailability(int $employeeId, string $startTime, string $endTime): bool {
      return !$this->bookingRepository->hasConflictingBookings($employeeId, $startTime, $endTime);
   }
}