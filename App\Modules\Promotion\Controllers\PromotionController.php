<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Promotion\Services\PromotionService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Promotions", description="Promosyon Yönetimi")
 */
class PromotionController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private PromotionService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Promotions"},
    *     path="/api/promotions",
    *     summary="Tüm Promosyonları Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Promosyon listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      $promotions = $this->service->getAllPromotions();
      return $this->success($promotions);
   }

   /**
    * @OA\Get(
    *     tags={"Promotions"},
    *     path="/api/promotions/{id}",
    *     summary="ID ile Promosyon Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Promosyon detayı"),
    *     @OA\Response(response=404, description="Promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getById(int $id) {
      $promotion = $this->service->getPromotionById($id);
      return $this->success($promotion);
   }

   /**
    * @OA\Get(
    *     tags={"Promotions"},
    *     path="/api/promotions/code/{code}",
    *     summary="Kod ile Promosyon Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="code",
    *         in="path",
    *         required=true,
    *         description="Promosyon Kodu",
    *         @OA\Schema(type="string")
    *     ),
    *     @OA\Response(response=200, description="Promosyon detayı"),
    *     @OA\Response(response=404, description="Promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByCode(string $code) {
      $promotion = $this->service->getPromotionByCode($code);
      return $this->success($promotion);
   }

   /**
    * @OA\Get(
    *     tags={"Promotions"},
    *     path="/api/promotions/active",
    *     summary="Aktif Promosyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Aktif promosyon listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getActive() {
      $promotions = $this->service->getActivePromotions();
      return $this->success($promotions);
   }

   /**
    * @OA\Get(
    *     tags={"Promotions"},
    *     path="/api/promotions/store/{storeId}",
    *     summary="Mağaza ID'sine Göre Promosyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="storeId",
    *         in="path",
    *         required=true,
    *         description="Mağaza ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Promosyon listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByStoreId(int $storeId) {
      $promotions = $this->service->getPromotionsByStoreId($storeId);
      return $this->success($promotions);
   }

   /**
    * @OA\Get(
    *     tags={"Promotions"},
    *     path="/api/promotions/service/{serviceId}",
    *     summary="Hizmet ID'sine Göre Promosyonları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="serviceId",
    *         in="path",
    *         required=true,
    *         description="Hizmet ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Promosyon listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getByServiceId(int $serviceId) {
      $promotions = $this->service->getPromotionsByServiceId($serviceId);
      return $this->success($promotions);
   }

   /**
    * @OA\Post(
    *     tags={"Promotions"},
    *     path="/api/promotions/create",
    *     summary="Yeni Promosyon Oluştur",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         description="Promosyon verileri",
    *         @OA\JsonContent(
    *             required={"code", "type", "value", "start_date", "end_date"},
    *             @OA\Property(property="code", type="string", example="WELCOME20"),
    *             @OA\Property(property="type", type="string", example="percentage", enum={"percentage", "fixed", "free_service"}),
    *             @OA\Property(property="value", type="number", example=20),
    *             @OA\Property(property="start_date", type="string", example="2023-01-01 00:00:00"),
    *             @OA\Property(property="end_date", type="string", example="2023-12-31 23:59:59"),
    *             @OA\Property(property="max_usage", type="integer", example=100),
    *             @OA\Property(property="user_usage_limit", type="integer", example=1),
    *             @OA\Property(property="min_order_amount", type="number", example=50),
    *             @OA\Property(property="applicable_to", type="string", example="all", enum={"all", "new_users", "referral", "specific_service"}),
    *             @OA\Property(property="service_id", type="integer", example=1),
    *             @OA\Property(property="store_id", type="integer", example=1),
    *             @OA\Property(property="status", type="string", example="active", enum={"active", "inactive"})
    *         )
    *     ),
    *     @OA\Response(response=201, description="Promosyon oluşturuldu"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=409, description="Promosyon kodu zaten var"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function create() {
      $data = $this->request->json();
      $promotion = $this->service->createPromotion($data);
      return $this->success($promotion);
   }

   /**
    * @OA\Put(
    *     tags={"Promotions"},
    *     path="/api/promotions/update/{id}",
    *     summary="Promosyon Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         description="Güncellenecek promosyon verileri",
    *         @OA\JsonContent(
    *             @OA\Property(property="code", type="string", example="WELCOME20"),
    *             @OA\Property(property="type", type="string", example="percentage", enum={"percentage", "fixed", "free_service"}),
    *             @OA\Property(property="value", type="number", example=20),
    *             @OA\Property(property="start_date", type="string", example="2023-01-01 00:00:00"),
    *             @OA\Property(property="end_date", type="string", example="2023-12-31 23:59:59"),
    *             @OA\Property(property="max_usage", type="integer", example=100),
    *             @OA\Property(property="user_usage_limit", type="integer", example=1),
    *             @OA\Property(property="min_order_amount", type="number", example=50),
    *             @OA\Property(property="applicable_to", type="string", example="all", enum={"all", "new_users", "referral", "specific_service"}),
    *             @OA\Property(property="service_id", type="integer", example=1),
    *             @OA\Property(property="store_id", type="integer", example=1),
    *             @OA\Property(property="status", type="string", example="active", enum={"active", "inactive"})
    *         )
    *     ),
    *     @OA\Response(response=200, description="Promosyon güncellendi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Promosyon bulunamadı"),
    *     @OA\Response(response=409, description="Promosyon kodu zaten var"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function update(int $id) {
      $data = $this->request->json();
      $success = $this->service->updatePromotion($id, $data);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to update promotion');
   }

   /**
    * @OA\Delete(
    *     tags={"Promotions"},
    *     path="/api/promotions/delete/{id}",
    *     summary="Promosyon Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Promosyon silindi"),
    *     @OA\Response(response=404, description="Promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function delete(int $id) {
      $success = $this->service->deletePromotion($id);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to delete promotion');
   }

   /**
    * @OA\Patch(
    *     tags={"Promotions"},
    *     path="/api/promotions/status/{id}",
    *     summary="Promosyon Durumunu Değiştir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         description="Promosyon ID",
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\RequestBody(
    *         required=true,
    *         description="Yeni durum",
    *         @OA\JsonContent(
    *             required={"status"},
    *             @OA\Property(property="status", type="string", example="active", enum={"active", "inactive"})
    *         )
    *     ),
    *     @OA\Response(response=200, description="Promosyon durumu değiştirildi"),
    *     @OA\Response(response=400, description="Geçersiz istek"),
    *     @OA\Response(response=404, description="Promosyon bulunamadı"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function changeStatus(int $id) {
      $data = $this->request->json();

      if (!isset($data['status'])) {
         return $this->error('Status is required');
      }

      $success = $this->service->changePromotionStatus($id, $data['status']);

      if ($success) {
         return $this->success();
      }

      return $this->error('Failed to change promotion status');
   }
}
