<?php

declare(strict_types=1);

namespace App\Modules\Service\Services;

use App\Core\Validation\Validator;
use App\Modules\Service\Models\Service;
use App\Modules\Service\Repositories\ServiceRepository;
use System\Exception\SystemException;

class ServiceService {
   // Define validation rules based on Service model
   private array $validationRules = [
      'category_id' => ['required', 'numeric'],
      'name' => ['required', 'max:100'],
      'description' => ['max:255'] // Description might be optional
   ];

   public function __construct(
      private ServiceRepository $repository,
      private Validator $validator // Inject Validator
   ) {
   }

   public function getAll(): array {
      $services = $this->repository->getAll();
      return array_map(function ($service) {
         return $service->jsonSerialize();
      }, $services);
   }

   public function getById(int $id): array {
      $service = $this->repository->getById($id);
      if (!$service) {
         throw new SystemException('Service not found');
      }
      return $service->jsonSerialize();
   }

   public function create(array $data): Service {
      // Add validation
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Check for existing name *after* validation
      if ($this->repository->existsByName($data['name'])) {
         throw new SystemException('Service name already exists');
      }
      return $this->repository->create($data);
   }

   public function update(int $id, array $data): ?object {
      // Add validation
      // Create rules dynamically for update, only validating provided fields
      $updateRules = array_intersect_key($this->validationRules, $data);
      // Ensure 'name' rule is included if name is provided, even if not required for update
      if (isset($data['name']) && !isset($updateRules['name'])) {
          $updateRules['name'] = $this->validationRules['name'];
      }
      if (!$this->validator->validate($data, $updateRules)) {
          throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Check for existing name *after* validation, only if name is being updated
      if (isset($data['name'])) {
          $existingService = $this->repository->findByName($data['name']);
          if ($existingService && $existingService->getId() !== $id) {
              throw new SystemException('Service name already exists');
          }
      }

      // Fetch the service *before* updating to ensure it exists
      $service = $this->repository->getById($id);
      if (!$service) {
          throw new SystemException('Service not found'); // Add check if service exists
      }

      return $this->repository->update($id, $data);
   }

   public function delete(int $id): bool {
      return $this->repository->delete($id);
   }

}
