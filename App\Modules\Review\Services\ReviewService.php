<?php

declare(strict_types=1);

namespace App\Modules\Review\Services;

use App\Core\Validation\Validator; // Add Validator import
use App\Modules\Review\Repositories\ReviewRepository;
use System\Exception\SystemException;

class ReviewService {
   // Define validation rules based on Review model
   private array $validationRules = [
      'service_id' => ['required', 'numeric'],
      'customer_id' => ['required', 'numeric'],
      'rating' => ['required', 'numeric', 'min:1', 'max:5'], // Use min/max rules if Validator supports them
      'review' => ['max:1000'], // Review text, optional, max length
      'response' => ['max:1000'], // Response text, optional, max length
      'status' => ['required', 'in:pending,approved,rejected'] // Example statuses
   ];

    // Rules for update (some fields might not be required/updatable)
    private array $updateValidationRules = [
        'rating' => ['required', 'numeric', 'min:1', 'max:5'],
        'review' => ['max:1000'],
        'response' => ['max:1000'],
        'status' => ['required', 'in:pending,approved,rejected']
    ];

   public function __construct(
      private ReviewRepository $repository,
      private Validator $validator // Inject Validator
   ) {
   }

   /**
    * Tüm yorumları getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $reviews = $this->repository->getAll();
      return array_map(function ($review) {
         return $review->jsonSerialize();
      }, $reviews);
   }

   /**
    * ID ile yorum getirir.
    *
    * @param int $id
    * @return array
    */
   public function getById(int $id): array {
      $review = $this->repository->getById($id);
      if (!$review) {
         throw new SystemException('Review not found');
      }
      return $review->jsonSerialize();
   }

   /**
    * Hizmet ID'sine göre yorumları getirir.
    *
    * @param int $serviceId
    * @return array
    */
   public function getByServiceId(int $serviceId): array {
      $reviews = $this->repository->getByServiceId($serviceId);
      return array_map(function ($review) {
         return $review->jsonSerialize();
      }, $reviews);
   }

   /**
    * Müşteri ID'sine göre yorumları getirir.
    *
    * @param int $customerId
    * @return array
    */
   public function getByCustomerId(int $customerId): array {
      $reviews = $this->repository->getByCustomerId($customerId);
      return array_map(function ($review) {
         return $review->jsonSerialize();
      }, $reviews);
   }

   /**
    * Duruma göre yorumları getirir.
    *
    * @param string $status
    * @return array
    */
   public function getByStatus(string $status): array {
      $reviews = $this->repository->getByStatus($status);
      return array_map(function ($review) {
         return $review->jsonSerialize();
      }, $reviews);
   }

   /**
    * Yeni yorum oluşturur.
    *
    * @param array $data
    * @return array
    */
   public function create(array $data): array {
      // Add validation using create rules
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Set default status if not provided
      $data['status'] = $data['status'] ?? 'pending';

      $review = $this->repository->create($data);
      return $review->jsonSerialize();
   }

   /**
    * Yorumu günceller.
    *
    * @param int $id
    * @param array $data
    * @return object|null
    */
   public function update(int $id, array $data): ?object {
      $review = $this->repository->getById($id);
      if (!$review) {
         throw new SystemException('Review not found');
      }

      // Validate provided data using update rules
      $updateRules = array_intersect_key($this->updateValidationRules, $data);
      if (!$this->validator->validate($data, $updateRules)) {
          throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Prevent updating service_id or customer_id
      unset($data['service_id']);
      unset($data['customer_id']);

      return $this->repository->update($id, $data);
   }

   /**
    * Yorumu siler.
    *
    * @param int $id
    * @return bool
    */
   public function delete(int $id): bool {
      $review = $this->repository->getById($id);
      if (!$review) {
         throw new SystemException('Review not found');
      }
      return $this->repository->delete($id);
   }

   /**
    * Yorum verilerini doğrular.
    *
    * @param array $data
    * @return void
    */
   // Remove the old private validateData method
   /*
   private function validateData(array $data): void {
      if (!isset($data['service_id'])) {
         throw new ExceptionHandler('Service ID is required');
      }

      if (!isset($data['customer_id'])) {
         throw new ExceptionHandler('Customer ID is required');
      }

      if (!isset($data['rating'])) {
         throw new ExceptionHandler('Rating is required');
      }

      if ($data['rating'] < 1 || $data['rating'] > 5) {
         throw new ExceptionHandler('Rating must be between 1 and 5');
      }
   }
   */
}
