<?php

declare(strict_types=1);

namespace App\Modules\Booking\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Booking\Models\Booking;
use System\Database\Database;
use System\Exception\ExceptionHandler;
use System\Exception\SystemException;

class BookingRepository extends BaseRepository {
   protected string $table = 'bookings';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   public function getTableName(): string {
      return $this->table;
   }

   /**
    * Müşteriye ait rezervasyonları getirir.
    *
    * @param int $customerId
    * @return array<Booking>
    * @throws ExceptionHandler
    */
   public function findByCustomer(int $customerId): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT *
             FROM bookings WHERE customer_id = :customer_id'
         );

         $stmt->execute(['customer_id' => $customerId]);
         $bookings = $stmt->getAll();

         return array_map(function ($row) {
            return $this->mapToObject((array)$row);
         }, $bookings);
      } catch (\PDOException $e) {
         throw new SystemException('Müşteriye ait rezervasyonlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * İşletmeye ait rezervasyonları getirir.
    *
    * @param int $storeId
    * @return array<Booking>
    * @throws ExceptionHandler
    */
   public function findByStore(int $storeId): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT *
             FROM bookings WHERE store_id = :store_id'
         );

         $stmt->execute(['store_id' => $storeId]);
         $bookings = $stmt->getAll();

         return array_map(function ($row) {
            return $this->mapToObject((array)$row);
         }, $bookings);
      } catch (\PDOException $e) {
         throw new SystemException('İşletmeye ait rezervasyonlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * Çalışana ait rezervasyonları getirir.
    *
    * @param int $employeeId
    * @return array<Booking>
    * @throws ExceptionHandler
    */
   public function findByEmployee(int $employeeId): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT *
             FROM bookings WHERE employee_id = :employee_id'
         );

         $stmt->execute(['employee_id' => $employeeId]);
         $bookings = $stmt->getAll();

         return array_map(function ($row) {
            return $this->mapToObject((array)$row);
         }, $bookings);
      } catch (\PDOException $e) {
         throw new SystemException('Çalışana ait rezervasyonlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir tarih aralığındaki rezervasyonları getirir.
    *
    * @param string $startDate
    * @param string $endDate
    * @return array<Booking>
    * @throws ExceptionHandler
    */
   public function findByDateRange(string $startDate, string $endDate): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT *
             FROM bookings
             WHERE start_time >= :start_date AND end_time <= :end_date'
         );

         $stmt->execute([
            'start_date' => $startDate,
            'end_date' => $endDate
         ]);
         $bookings = $stmt->getAll();

         return array_map(function ($row) {
            return $this->mapToObject((array)$row);
         }, $bookings);
      } catch (\PDOException $e) {
         throw new SystemException('Tarih aralığına göre rezervasyonlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * Belirli bir durumdaki rezervasyonları getirir.
    *
    * @param string $status
    * @return array<Booking>
    * @throws ExceptionHandler
    */
   public function findByStatus(string $status): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT *
             FROM bookings WHERE status = :status'
         );

         $stmt->execute(['status' => $status]);
         $bookings = $stmt->getAll();

         return array_map(function ($row) {
            return $this->mapToObject((array)$row);
         }, $bookings);
      } catch (\PDOException $e) {
         throw new SystemException('Durum bilgisine göre rezervasyonlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * Yeni rezervasyon oluşturur.
    *
    * @param array $data
    * @return Booking|null
    * @throws ExceptionHandler
    */
   public function create(array $data): ?Booking {
      try {
         $stmt = $this->database->prepare(
            'INSERT INTO bookings (customer_id, store_id, service_id, employee_id, start_time, end_time, final_price, note, status)
             VALUES (:customer_id, :store_id, :service_id, :employee_id, :start_time, :end_time, :final_price, :note, :status)'
         );
         $stmt->execute([
            'customer_id' => $data['customer_id'],
            'store_id' => $data['store_id'],
            'service_id' => $data['service_id'],
            'employee_id' => $data['employee_id'],
            'start_time' => $data['start_time'],
            'end_time' => $data['end_time'],
            'final_price' => $data['final_price'],
            'note' => $data['note'] ?? null,
            'status' => $data['status'] ?? 'pending'
         ]);
      return $this->getById((int)$this->database->getLastId());
      } catch (\PDOException $e) {
         throw new SystemException('Rezervasyon oluşturma işlemi başarısız oldu: ' . $e->getMessage());
      }
   }

   /**
    * Rezervasyon günceller.
    *
    * @param int $bookingId
    * @param array $data
    * @return Booking|null
    */
   public function update(int $bookingId, array $data): ?Booking {
      try {
         $stmt = $this->database->prepare(
            'UPDATE bookings SET
            customer_id = :customer_id,
            store_id = :store_id,
            service_id = :service_id,
            employee_id = :employee_id,
            start_time = :start_time,
            end_time = :end_time,
            final_price = :final_price,
            note = :note,
            status = :status
         WHERE id = :id'
         );
         $stmt->execute([
            'customer_id' => $data['customer_id'],
            'store_id' => $data['store_id'],
            'service_id' => $data['service_id'],
            'employee_id' => $data['employee_id'],
            'start_time' => $data['start_time'],
            'end_time' => $data['end_time'],
            'final_price' => $data['final_price'],
            'note' => $data['note'] ?? null,
            'status' => $data['status'],
            'id' => $bookingId
         ]);
         return $this->getById($bookingId);
      } catch (\Exception $e) {
         throw new SystemException($e->getMessage());
      }
   }

   /**
    * Rezervasyon durumunu günceller.
    *
    * @param int $bookingId
    * @param string $status
    * @return bool
    */
   public function updateStatus(int $bookingId, string $status): bool {
      try {
         $stmt = $this->database->prepare(
            'UPDATE bookings SET status = :status WHERE id = :id'
         );
         $stmt->execute([
            'status' => $status,
            'id' => $bookingId
         ]);
         return $stmt->getAffectedRows() > 0;
      } catch (\Exception $e) {
         throw new SystemException($e->getMessage());
      }
   }

   /**
    * Çakışan rezervasyonları kontrol eder.
    *
    * @param int $employeeId
    * @param string $startTime
    * @param string $endTime
    * @param int|null $excludeBookingId
    * @return bool
    */
   public function hasConflictingBookings(int $employeeId, string $startTime, string $endTime, ?int $excludeBookingId = null): bool {
      $sql = 'SELECT COUNT(*) as count FROM bookings
              WHERE employee_id = :employee_id
              AND status IN ("pending", "confirmed")
              AND ((start_time <= :end_time AND end_time >= :start_time))';

      $params = [
         'employee_id' => $employeeId,
         'start_time' => $startTime,
         'end_time' => $endTime
      ];

      if ($excludeBookingId) {
         $sql .= ' AND id != :exclude_id';
         $params['exclude_id'] = $excludeBookingId;
      }

      $stmt = $this->database->prepare($sql);
      $stmt->execute($params);
      $result = $stmt->getRow();

      return $result->count > 0;
   }

   /**
    * Veritabanı satırını Booking nesnesine dönüştürür.
    *
    * @param array $data
    * @return Booking
    */
   protected function mapToObject(array $data): Booking {
      return new Booking(
         $data['id'] ?? null,
         $data['customer_id'] ?? null,
         $data['store_id'] ?? null,
         $data['service_id'] ?? null,
         $data['employee_id'] ?? null,
         $data['start_time'] ?? null,
         $data['end_time'] ?? null,
         isset($data['final_price']) ? floatval($data['final_price']) : null,
         $data['note'] ?? null,
         $data['status'] ?? null,
         $data['created_at'] ?? null,
         $data['updated_at'] ?? null
     );
   }
}