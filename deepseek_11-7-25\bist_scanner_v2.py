import pandas as pd
import numpy as np
import random
import time
import warnings
from tvDatafeed import TvDatafeed, Interval
from datetime import datetime, timedelta
from scipy.stats import linregress
from scipy.signal import find_peaks, find_peaks_cwt
from config import TV_USERNAME, TV_PASSWORD
from tvDatafeed import TvDatafeed
from fp.fp import FreeProxy
warnings.filterwarnings('ignore')

# ----------------------------
# VERİ YÖNETİM MODÜLÜ
# ----------------------------

class BISTDataFetcher:
    def __init__(self):
        self.tv = TvDatafeed(TV_USERNAME, TV_PASSWORD)

    def get_bist30_symbols(self):
        return [
            # 'AKBNK', 'ARCLK', 'ASELS', 'BIMAS', 'DOHOL',
            # 'EKGYO', 'ENJSA', 'EREGL', 'FROTO', 'GARAN',
            # 'GUBRF', 'HALKB', 'HEKTS', 'ISCTR', 'ISGYO',
            # 'KCHOL', 'KOZAA', 'KOZAL', 'KRDMD', 'MGROS',
            # 'ODAS', 'OYAKC', 'PETKM', 'PGSUS', 'SAHOL',
            # 'SASA', 'SISE', 'TAVHL', 'TCELL', 'THYAO',
            # 'TKFEN', 'TOASO', 'TTKOM', 'TUPRS', 'ULKER',
            'VESTL', 'YKBNK'
        ]

    def fetch_stock_data(self, symbol, n_bars=500):
        """Hisse verilerini çeker ve temizler"""
        try:
            df = self.tv.get_hist(
                symbol=symbol,
                exchange='BIST',
                interval=Interval.in_daily,
               #  interval=Interval.in_1_hour,
                n_bars=n_bars
            )

            if len(df.columns) == 5:
                df.columns = ['open', 'high', 'low', 'close', 'volume']

            # Veri kontrolü
            if len(df) < 100:
                return None

            df = df.dropna()
            return df

        except Exception as e:
            print(f"Veri çekme hatası ({symbol}): {str(e)}")
            return None

# ----------------------------
# RİSK YÖNETİM MODÜLÜ
# ----------------------------
class RiskManager:
    def __init__(self):
        self.min_volume = 1000000
        self.min_price = 1.0
        self.max_volatility = 0.05

    def calculate_risk_metrics(self, df):
        """Risk metriklerini hesaplar"""
        try:
            current_price = df['close'].iloc[-1]
            returns = df['close'].pct_change().dropna()

            # Volatilite
            volatility = returns.std() * np.sqrt(252)

            # Max Drawdown
            peak = df['close'].expanding().max()
            drawdown = (df['close'] - peak) / peak
            max_drawdown = drawdown.min()

            # Beta (XU100 ile korelasyon yaklaşımı)
            market_returns = returns.rolling(20).mean()
            beta = np.corrcoef(returns[-60:], market_returns[-60:])[0, 1] if len(returns) > 60 else 1.0

            return {
                'volatility': volatility,
                'max_drawdown': max_drawdown,
                'beta': beta,
                'current_price': current_price,
                'avg_volume': df['volume'].mean()
            }
        except:
            return None

    def calculate_position_size(self, account_balance, risk_per_trade, entry_price, stop_loss):
        """Pozisyon büyüklüğünü hesaplar"""
        if stop_loss >= entry_price:
            return 0

        risk_amount = account_balance * risk_per_trade
        risk_per_share = entry_price - stop_loss
        position_size = risk_amount / risk_per_share

        return int(position_size)

    def calculate_targets_and_stops(self, df, signal_type):
        """Hedef ve stop seviyelerini hesaplar"""
        current_price = df['close'].iloc[-1]
        atr = df['ATR'].iloc[-1] if 'ATR' in df.columns else df['close'].pct_change().std() * current_price

        # Support/Resistance seviyeleri
        support_levels = self._find_support_levels(df)
        resistance_levels = self._find_resistance_levels(df)

        if signal_type == "volatility_breakout":
            target_1 = current_price + (atr * 2)
            target_2 = current_price + (atr * 3.5)
            stop_loss = current_price - (atr * 1.5)

        elif signal_type == "momentum":
            target_1 = resistance_levels[0] if resistance_levels else current_price * 1.05
            target_2 = resistance_levels[1] if len(resistance_levels) > 1 else current_price * 1.10
            stop_loss = support_levels[0] if support_levels else current_price * 0.95

        elif signal_type == "pattern":
            target_1 = current_price * 1.08
            target_2 = current_price * 1.15
            stop_loss = current_price * 0.93

        else:
            target_1 = current_price * 1.05
            target_2 = current_price * 1.10
            stop_loss = current_price * 0.95

        risk_reward = (target_1 - current_price) / (current_price - stop_loss) if stop_loss < current_price else 0

        return {
            'target_1': target_1,
            'target_2': target_2,
            'stop_loss': stop_loss,
            'risk_reward': risk_reward,
            'atr': atr
        }

    def _find_support_levels(self, df, window=20):
       """Geçmiş verilere bakarak destek seviyelerini bulur (Look-ahead bias olmadan)"""
       # rolling.min() geçmişteki en düşüğü bulur, bu daha güvenli bir yaklaşımdır.
       local_minima_indices, _ = find_peaks(-df['low'], distance=window//2)
       if len(local_minima_indices) == 0:
          return []

       # Sadece mevcut fiyattan daha düşük olan destekleri al
       current_price = df['close'].iloc[-1]
       support_levels = df['low'].iloc[local_minima_indices]
       relevant_supports = support_levels[support_levels < current_price].unique()
       return sorted(relevant_supports, reverse=True)[:3] # Fiyata en yakın 3 destek

    def _find_resistance_levels(self, df, window=20):
       """Geçmiş verilere bakarak direnç seviyelerini bulur (Look-ahead bias olmadan)"""
       local_maxima_indices, _ = find_peaks(df['high'], distance=window//2)
       if len(local_maxima_indices) == 0:
          return []

       # Sadece mevcut fiyattan daha yüksek olan dirençleri al
       current_price = df['close'].iloc[-1]
       resistance_levels = df['high'].iloc[local_maxima_indices]
       relevant_resistances = resistance_levels[resistance_levels > current_price].unique()
       return sorted(relevant_resistances)[:3] # Fiyata en yakın 3 direnç

# ----------------------------
# FORMASYON TESPİT MODÜLÜ
# ----------------------------
class PatternDetector:
    def __init__(self):
        self.min_pattern_length = 10

    def detect_head_and_shoulders(self, df):
        """Baş-omuz formasyonu tespiti"""
        if len(df) < 50:
            return False

        highs = df['high'].values
        peaks, _ = find_peaks(highs, distance=5, prominence=np.std(highs) * 0.5)

        if len(peaks) < 3:
            return False

        # Son 3 peak'i al
        recent_peaks = peaks[-3:]
        peak_values = [highs[i] for i in recent_peaks]

        # Baş-omuz kontrolü: ortadaki en yüksek, yanlar benzer
        if len(peak_values) == 3:
            left_shoulder, head, right_shoulder = peak_values

            # Baş en yüksek olmalı
            if head > left_shoulder and head > right_shoulder:
                # Omuzlar benzer yükseklikte (±5% tolerans)
                shoulder_diff = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder)
                if shoulder_diff < 0.05:
                    return True

        return False

    def detect_inverse_head_and_shoulders(self, df):
        """Ters baş-omuz formasyonu tespiti"""
        if len(df) < 50:
            return False

        lows = df['low'].values
        valleys, _ = find_peaks(-lows, distance=5, prominence=np.std(lows) * 0.5)

        if len(valleys) < 3:
            return False

        recent_valleys = valleys[-3:]
        valley_values = [lows[i] for i in recent_valleys]

        if len(valley_values) == 3:
            left_shoulder, head, right_shoulder = valley_values

            # Baş en düşük olmalı
            if head < left_shoulder and head < right_shoulder:
                shoulder_diff = abs(left_shoulder - right_shoulder) / max(left_shoulder, right_shoulder)
                if shoulder_diff < 0.05:
                    return True

        return False

    def detect_double_top(self, df):
        """Çift tepe formasyonu tespiti"""
        if len(df) < 30:
            return False

        highs = df['high'].values[-30:]  # Son 30 gün
        peaks, _ = find_peaks(highs, distance=3, prominence=np.std(highs) * 0.3)

        if len(peaks) < 2:
            return False

        # Son 2 peak'i kontrol et
        recent_peaks = peaks[-2:]
        peak_values = [highs[i] for i in recent_peaks]

        if len(peak_values) == 2:
            first_peak, second_peak = peak_values
            # İki tepe benzer yükseklikte (±3% tolerans)
            peak_diff = abs(first_peak - second_peak) / max(first_peak, second_peak)
            if peak_diff < 0.03:
                return True

        return False

    def detect_double_bottom(self, df):
        """Çift dip formasyonu tespiti"""
        if len(df) < 30:
            return False

        lows = df['low'].values[-30:]
        valleys, _ = find_peaks(-lows, distance=3, prominence=np.std(lows) * 0.3)

        if len(valleys) < 2:
            return False

        recent_valleys = valleys[-2:]
        valley_values = [lows[i] for i in recent_valleys]

        if len(valley_values) == 2:
            first_valley, second_valley = valley_values
            valley_diff = abs(first_valley - second_valley) / max(first_valley, second_valley)
            if valley_diff < 0.03:
                return True

        return False

    def detect_triangle_pattern(self, df):
        """Üçgen formasyonu tespiti"""
        if len(df) < 40:
            return False

        # Son 40 günün verilerini al
        recent_data = df.tail(40)

        # Yüksek ve düşük noktaları bul
        highs = recent_data['high'].values
        lows = recent_data['low'].values

        high_peaks, _ = find_peaks(highs, distance=3)
        low_valleys, _ = find_peaks(-lows, distance=3)

        if len(high_peaks) < 3 or len(low_valleys) < 3:
            return False

        # Trend çizgilerinin eğimini hesapla
        high_trend = np.polyfit(high_peaks, highs[high_peaks], 1)
        low_trend = np.polyfit(low_valleys, lows[low_valleys], 1)

        # Daralan üçgen: üst trend aşağı, alt trend yukarı
        if high_trend[0] < 0 and low_trend[0] > 0:
            return True

        return False

    def detect_flag_pattern(self, df):
        """Bayrak formasyonu tespiti"""
        if len(df) < 20:
            return False

        # Güçlü trend sonrası konsolidasyon
        recent_data = df.tail(20)

        # Trend gücü kontrolü (son 10 gün önceki 10 günle karşılaştır)
        first_half = recent_data.head(10)['close'].mean()
        second_half = recent_data.tail(10)['close'].mean()

        trend_strength = abs(second_half - first_half) / first_half

        # %5'ten fazla hareket varsa ve son 5 gün dar bantta ise
        if trend_strength > 0.05:
            last_5_days = recent_data.tail(5)
            volatility = last_5_days['close'].std() / last_5_days['close'].mean()

            if volatility < 0.02:  # Düşük volatilite
                return True

        return False

# ----------------------------
# BACKTEST MOTORU
# ----------------------------
class BacktestEngine:
    def __init__(self, initial_capital=100000):
        self.initial_capital = initial_capital
        self.commission = 0.001  # %0.1 komisyon

    def run_backtest(self, df, signals, strategy_name):
        """Backtest çalıştırır"""
        capital = self.initial_capital
        positions = []
        trades = []

        for i, signal in enumerate(signals):
            if signal:
                # Pozisyon aç
                entry_price = df['close'].iloc[i]
                position_size = int(capital * 0.1 / entry_price)  # %10 risk

                if position_size > 0:
                    # Stop loss ve hedef belirle
                    stop_loss = entry_price * 0.95
                    target = entry_price * 1.10

                    # Pozisyon takibi
                    for j in range(i + 1, min(i + 20, len(df))):  # 20 gün takip
                        current_price = df['close'].iloc[j]

                        # Stop loss
                        if current_price <= stop_loss:
                            pnl = (current_price - entry_price) * position_size
                            capital += pnl - (entry_price * position_size * self.commission * 2)
                            trades.append({
                                'entry_date': df.index[i],
                                'exit_date': df.index[j],
                                'entry_price': entry_price,
                                'exit_price': current_price,
                                'pnl': pnl,
                                'type': 'STOP_LOSS'
                            })
                            break

                        # Target
                        elif current_price >= target:
                            pnl = (current_price - entry_price) * position_size
                            capital += pnl - (entry_price * position_size * self.commission * 2)
                            trades.append({
                                'entry_date': df.index[i],
                                'exit_date': df.index[j],
                                'entry_price': entry_price,
                                'exit_price': current_price,
                                'pnl': pnl,
                                'type': 'TARGET'
                            })
                            break

        # Performans metrikleri
        if trades:
            total_pnl = sum([trade['pnl'] for trade in trades])
            win_rate = len([t for t in trades if t['pnl'] > 0]) / len(trades)
            avg_win = np.mean([t['pnl'] for t in trades if t['pnl'] > 0]) if any(t['pnl'] > 0 for t in trades) else 0
            avg_loss = np.mean([t['pnl'] for t in trades if t['pnl'] < 0]) if any(t['pnl'] < 0 for t in trades) else 0

            return {
                'total_return': (capital - self.initial_capital) / self.initial_capital,
                'total_trades': len(trades),
                'win_rate': win_rate,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'profit_factor': abs(avg_win / avg_loss) if avg_loss != 0 else 0,
                'trades': trades
            }

        return None

# ----------------------------
# GELİŞMİŞ ANALİTİK MOTORU
# ----------------------------
class AdvancedAnalytics:
    def __init__(self):
        self.pattern_detector = PatternDetector()
        self.backtest_engine = BacktestEngine()

    def calculate_technical_indicators(self, df):
        """Gelişmiş teknik göstergeleri hesaplar"""
        # Temel göstergeler
        df['MA_10'] = df['close'].rolling(10).mean()
        df['MA_20'] = df['close'].rolling(20).mean()
        df['MA_50'] = df['close'].rolling(50).mean()
        df['MA_200'] = df['close'].rolling(200).mean()

        # EMA'lar
        df['EMA_12'] = df['close'].ewm(span=12).mean()
        df['EMA_26'] = df['close'].ewm(span=26).mean()

        # ATR
        high_low = df['high'] - df['low']
        high_close = np.abs(df['high'] - df['close'].shift())
        low_close = np.abs(df['low'] - df['close'].shift())
        ranges = pd.concat([high_low, high_close, low_close], axis=1)
        true_range = np.max(ranges, axis=1)
        df['ATR'] = true_range.rolling(14).mean()

        # RSI
        delta = df['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.ewm(alpha=1/14, adjust=False).mean()
        avg_loss = loss.ewm(alpha=1/14, adjust=False).mean()
        rs = avg_gain / avg_loss
        df['RSI'] = 100 - (100 / (1 + rs))

        # MACD
        df['MACD'] = df['EMA_12'] - df['EMA_26']
        df['MACD_Signal'] = df['MACD'].ewm(span=9).mean()
        df['MACD_Histogram'] = df['MACD'] - df['MACD_Signal']

        # Bollinger Bands
        df['BB_Middle'] = df['close'].rolling(20).mean()
        bb_std = df['close'].rolling(20).std()
        df['BB_Upper'] = df['BB_Middle'] + (bb_std * 2)
        df['BB_Lower'] = df['BB_Middle'] - (bb_std * 2)
        df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']

        # VWAP
        df['VWAP'] = (df['volume'] * (df['high'] + df['low'] + df['close']) / 3).cumsum() / df['volume'].cumsum()

        # Hacim göstergeleri
        df['Volume_MA'] = df['volume'].rolling(20).mean()
        df['Volume_Ratio'] = df['volume'] / df['Volume_MA']

        # Stochastic
        low_min = df['low'].rolling(14).min()
        high_max = df['high'].rolling(14).max()
        df['Stoch_K'] = 100 * (df['close'] - low_min) / (high_max - low_min)
        df['Stoch_D'] = df['Stoch_K'].rolling(3).mean()

        # OBV (On Balance Volume)
        df['OBV'] = (np.sign(df['close'].diff()) * df['volume']).fillna(0).cumsum()

        return df.dropna()

    def signal_scoring_system(self, df):
        """Sinyal skorlama sistemi"""
        score = 0
        factors = []

        if len(df) < 50:
            return 0, []

        latest = df.iloc[-1]

        # Trend skoru
        if latest['close'] > latest['MA_20']:
            score += 15
            factors.append("MA20 Üzerinde")
        if latest['close'] > latest['MA_50']:
            score += 10
            factors.append("MA50 Üzerinde")
        if latest['MA_20'] > latest['MA_50']:
            score += 10
            factors.append("Kısa Trend Pozitif")

        # Momentum skoru
        if latest['RSI'] > 50 and latest['RSI'] < 70:
            score += 10
            factors.append("RSI Optimum")
        elif latest['RSI'] > 70:
            score -= 5
            factors.append("RSI Aşırı Alım")

        # MACD skoru
        if latest['MACD'] > latest['MACD_Signal']:
            score += 10
            factors.append("MACD Pozitif")

        # Hacim skoru
        if latest['Volume_Ratio'] > 1.5:
            score += 15
            factors.append("Yüksek Hacim")
        elif latest['Volume_Ratio'] > 1.2:
            score += 5
            factors.append("Orta Hacim")

        # Bollinger Band skoru
        if latest['close'] > latest['BB_Upper']:
            score += 5
            factors.append("BB Üst Bant Kırımı")
        elif latest['close'] < latest['BB_Lower']:
            score -= 5
            factors.append("BB Alt Bant Altında")

        # Pattern skoru
        if self.pattern_detector.detect_double_bottom(df):
            score += 20
            factors.append("Çift Dip")
        elif self.pattern_detector.detect_inverse_head_and_shoulders(df):
            score += 25
            factors.append("Ters Baş-Omuz")
        elif self.pattern_detector.detect_triangle_pattern(df):
            score += 15
            factors.append("Üçgen Formasyonu")

        return score, factors

    def volatility_breakout_signal(self, df, threshold=1.5):
        """Volatilite patlaması sinyali"""
        if len(df) < 50:
            return False

        latest = df.iloc[-1]

        # Bollinger Band daralmasi
        bb_squeeze = latest['BB_Width'] < df['BB_Width'].rolling(20).mean() * 0.7

        # Hacim patlaması
        volume_breakout = latest['Volume_Ratio'] > threshold

        # Fiyat patlaması
        price_breakout = latest['close'] > latest['BB_Upper']

        # RSI momentum
        rsi_momentum = latest['RSI'] > 55

        return bb_squeeze and volume_breakout and price_breakout and rsi_momentum

    def smart_money_signal(self, df):
        """Akıllı para sinyali"""
        if len(df) < 30:
            return False

        latest = df.iloc[-1]

        # OBV trend
        obv_trend = latest['OBV'] > df['OBV'].rolling(10).mean()

        # VWAP pozisyonu
        vwap_bullish = latest['close'] > latest['VWAP'] * 1.005

        # Hacim trend
        volume_trend = latest['Volume_MA'] > df['Volume_MA'].rolling(10).mean()

        # Fiyat trend
        price_trend = latest['close'] > latest['MA_20']

        return obv_trend and vwap_bullish and volume_trend and price_trend

    def momentum_acceleration_signal(self, df):
        """Momentum hızlanması sinyali"""
        if len(df) < 50:
            return False

        latest = df.iloc[-1]

        # MACD momentum
        macd_bullish = latest['MACD'] > latest['MACD_Signal']
        macd_acceleration = latest['MACD_Histogram'] > df['MACD_Histogram'].iloc[-2]

        # RSI trend
        rsi_trend = latest['RSI'] > 55 and latest['RSI'] < 75

        # Stochastic
        stoch_bullish = latest['Stoch_K'] > latest['Stoch_D'] and latest['Stoch_K'] > 20

        # Moving average alignment
        ma_alignment = latest['MA_10'] > latest['MA_20'] > latest['MA_50']

        return macd_bullish and macd_acceleration and rsi_trend and stoch_bullish and ma_alignment

    def pattern_breakout_signal(self, df):
        """Formasyon kırılım sinyali"""
        if len(df) < 40:
            return False

        signals = []

        # Çift dip kırılımı
        if self.pattern_detector.detect_double_bottom(df):
            signals.append("Çift Dip Kırılımı")

        # Ters baş-omuz
        if self.pattern_detector.detect_inverse_head_and_shoulders(df):
            signals.append("Ters Baş-Omuz")

        # Üçgen kırılımı
        if self.pattern_detector.detect_triangle_pattern(df):
            latest = df.iloc[-1]
            # Üçgen üst sınırını kırma kontrolü
            if latest['close'] > df['high'].rolling(10).max().iloc[-2]:
                signals.append("Üçgen Yukarı Kırılımı")

        # Bayrak formasyonu
        if self.pattern_detector.detect_flag_pattern(df):
            signals.append("Bayrak Formasyonu")

        return len(signals) > 0, signals

# ----------------------------
# SINYAL FİLTRELEME SİSTEMİ
# ----------------------------
class SignalFilter:
    def __init__(self):
        self.min_score = 40
        self.min_volume = 1000000
        self.min_price = 1.0
        self.max_rsi = 80
        self.min_rsi = 20

    def filter_signals(self, signals):
        """Sinyalleri filtreler ve sıralar"""
        filtered_signals = []

        for signal in signals:
            # Minimum skor kontrolü
            if signal['score'] < self.min_score:
                continue

            # Hacim kontrolü
            if signal['volume'] < self.min_volume:
                continue

            # Fiyat kontrolü
            if signal['price'] < self.min_price:
                continue

            # RSI kontrolü
            if signal.get('rsi', 50) > self.max_rsi or signal.get('rsi', 50) < self.min_rsi:
                continue

            # Risk-reward oranı kontrolü
            if signal.get('risk_reward', 0) < 1.5:
                continue

            filtered_signals.append(signal)

        # Skora göre sırala
        filtered_signals.sort(key=lambda x: x['score'], reverse=True)

        return filtered_signals

# ----------------------------
# RAPORLAMA VE ÇIKTI
# ----------------------------
class ReportGenerator:
    @staticmethod
    def generate_comprehensive_report(signals, filename="bist_comprehensive_report.html"):
        """Kapsamlı HTML raporu oluşturur"""

        # Performans özeti
        total_signals = len(signals)
        high_score_signals = len([s for s in signals if s['score'] > 60])
        avg_score = np.mean([s['score'] for s in signals]) if signals else 0

        report = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>BIST Profesyonel Sinyal Raporu</title>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: 'Arial', sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }}

                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    border-radius: 10px;
                    margin-bottom: 30px;
                    text-align: center;
                }}

                .summary {{
                    display: flex;
                    justify-content: space-around;
                    margin-bottom: 30px;
                }}

                .summary-card {{
                    background: white;
                    padding: 20px;
                    border-radius: 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                    text-align: center;
                    min-width: 150px;
                }}

                .summary-card h3 {{
                    margin: 0;
                    color: #333;
                    font-size: 2em;
                }}

                .summary-card p {{
                    margin: 10px 0 0 0;
                    color: #666;
                }}

                table {{
                    width: 100%;
                    border-collapse: collapse;
                    background: white;
                    border-radius: 10px;
                    overflow: hidden;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }}

                th, td {{
                    padding: 15px;
                    text-align: left;
                    border-bottom: 1px solid #eee;
                }}

                th {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    font-weight: bold;
                }}

                tr:hover {{
                    background-color: #f8f9fa;
                }}

                .score-high {{
                    background: #d4edda;
                    color: #155724;
                    padding: 5px 10px;
                    border-radius: 5px;
                    font-weight: bold;
                }}

                .score-medium {{
                    background: #fff3cd;
                    color: #856404;
                    padding: 5px 10px;
                    border-radius: 5px;
                    font-weight: bold;
                }}

                .score-low {{
                    background: #f8d7da;
                    color: #721c24;
                    padding: 5px 10px;
                    border-radius: 5px;
                    font-weight: bold;
                }}

                .positive {{
                    color: #28a745;
                    font-weight: bold;
                }}

                .negative {{
                    color: #dc3545;
                    font-weight: bold;
                }}

                .target-info {{
                    background: #e3f2fd;
                    padding: 10px;
                    border-radius: 5px;
                    margin: 5px 0;
                    font-size: 0.9em;
                }}

                .risk-info {{
                    background: #fff3e0;
                    padding: 10px;
                    border-radius: 5px;
                    margin: 5px 0;
                    font-size: 0.9em;
                }}

                .pattern-badge {{
                    background: #6f42c1;
                    color: white;
                    padding: 3px 8px;
                    border-radius: 12px;
                    font-size: 0.8em;
                    margin: 2px;
                    display: inline-block;
                }}

                .footer {{
                    margin-top: 30px;
                    padding: 20px;
                    background: #333;
                    color: white;
                    text-align: center;
                    border-radius: 10px;
                }}
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎯 BIST Profesyonel Sinyal Raporu</h1>
                <p>Oluşturulma: {datetime.now().strftime("%d/%m/%Y %H:%M")}</p>
            </div>

            <div class="summary">
                <div class="summary-card">
                    <h3>{total_signals}</h3>
                    <p>Toplam Sinyal</p>
                </div>
                <div class="summary-card">
                    <h3>{high_score_signals}</h3>
                    <p>Yüksek Skor (>60)</p>
                </div>
                <div class="summary-card">
                    <h3>{avg_score:.1f}</h3>
                    <p>Ortalama Skor</p>
                </div>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Hisse</th>
                        <th>Skor</th>
                        <th>Strateji</th>
                        <th>Fiyat Bilgileri</th>
                        <th>Risk-Getiri</th>
                        <th>Teknik Faktörler</th>
                        <th>Formasyonlar</th>
                    </tr>
                </thead>
                <tbody>"""

        for signal in signals:
            # Skor rengi
            if signal['score'] > 70:
                score_class = "score-high"
            elif signal['score'] > 50:
                score_class = "score-medium"
            else:
                score_class = "score-low"

            # Formasyonlar
            patterns = ""
            if signal.get('patterns'):
                for pattern in signal['patterns']:
                    patterns += f'<span class="pattern-badge">{pattern}</span> '

            # Fiyat bilgileri
            price_info = f"""
                <div>Mevcut: {signal['price']:.2f} ₺</div>
                <div class="{'positive' if signal['change'] > 0 else 'negative'}">
                    Değişim: {signal['change']:.2f}%
                </div>
                <div>Hacim: {signal['volume']/1000000:.1f}M</div>
            """

            # Risk-getiri bilgileri
            risk_info = f"""
                <div class="target-info">
                    <div>🎯 Hedef 1: {signal.get('target_1', 0):.2f} ₺</div>
                    <div>🎯 Hedef 2: {signal.get('target_2', 0):.2f} ₺</div>
                </div>
                <div class="risk-info">
                    <div>🛡️ Stop: {signal.get('stop_loss', 0):.2f} ₺</div>
                    <div>📊 R/R: {signal.get('risk_reward', 0):.1f}</div>
                </div>
            """

            # Teknik faktörler
            factors = '<br>'.join([f'✓ {factor}' for factor in signal.get('factors', [])])

            report += f"""
                <tr>
                    <td><strong>{signal['symbol']}</strong></td>
                    <td><span class="{score_class}">{signal['score']}</span></td>
                    <td>{signal['strategy']}</td>
                    <td>{price_info}</td>
                    <td>{risk_info}</td>
                    <td>{factors}</td>
                    <td>{patterns}</td>
                </tr>
            """

        report += f"""
                </tbody>
            </table>

            <div class="footer">
                <p>⚠️ Bu rapor sadece bilgilendirme amaçlıdır. Yatırım kararlarınızı kendi riskinize alın.</p>
                <p>📊 Veriler TradingView'den alınmıştır. Gerçek zamanlı verilerle doğrulayın.</p>
            </div>
        </body>
        </html>
        """

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)

        print(f"📊 Kapsamlı rapor oluşturuldu: {filename}")

# ----------------------------
# ANA İŞLEM SINIFI
# ----------------------------
class BISTAnalyzer:
    def __init__(self):
        self.fetcher = BISTDataFetcher()
        self.analytics = AdvancedAnalytics()
        self.risk_manager = RiskManager()
        self.signal_filter = SignalFilter()
        self.report_generator = ReportGenerator()

    def analyze_symbol(self, symbol):
        """Tek bir hisse için kapsamlı analiz"""
        print(f"📈 Analiz ediliyor: {symbol}")

        # Veri çekme
        df = self.fetcher.fetch_stock_data(symbol)
        if df is None or len(df) < 100:
            print(f"  ❌ {symbol} için yeterli veri yok")
            return None

        # Teknik göstergeler
        df = self.analytics.calculate_technical_indicators(df)

        # Risk metrikleri
        risk_metrics = self.risk_manager.calculate_risk_metrics(df)
        if risk_metrics is None:
            return None

        # Sinyal tespiti
        signals = []

        # Volatilite sinyali
        if self.analytics.volatility_breakout_signal(df):
            targets = self.risk_manager.calculate_targets_and_stops(df, "volatility_breakout")
            signals.append({
                'strategy': 'Volatilite Patlaması',
                'type': 'volatility_breakout',
                'targets': targets
            })

        # Akıllı para sinyali
        if self.analytics.smart_money_signal(df):
            targets = self.risk_manager.calculate_targets_and_stops(df, "momentum")
            signals.append({
                'strategy': 'Akıllı Para Akışı',
                'type': 'smart_money',
                'targets': targets
            })

        # Momentum sinyali
        if self.analytics.momentum_acceleration_signal(df):
            targets = self.risk_manager.calculate_targets_and_stops(df, "momentum")
            signals.append({
                'strategy': 'Momentum Hızlanması',
                'type': 'momentum',
                'targets': targets
            })

        # Formasyon sinyali
        pattern_signal, patterns = self.analytics.pattern_breakout_signal(df)
        if pattern_signal:
            targets = self.risk_manager.calculate_targets_and_stops(df, "pattern")
            signals.append({
                'strategy': 'Formasyon Kırılımı',
                'type': 'pattern',
                'targets': targets,
                'patterns': patterns
            })

        # Sinyal skorlama
        score, factors = self.analytics.signal_scoring_system(df)

        # Sonuçları birleştir
        if signals:
            latest = df.iloc[-1]

            # En iyi sinyali seç
            best_signal = signals[0]

            result = {
                'symbol': symbol,
                'score': score,
                'strategy': best_signal['strategy'],
                'type': best_signal['type'],
                'price': latest['close'],
                'change': (latest['close'] - df.iloc[-2]['close']) / df.iloc[-2]['close'] * 100,
                'volume': latest['volume'],
                'rsi': latest['RSI'],
                'factors': factors,
                'patterns': best_signal.get('patterns', []),
                'target_1': best_signal['targets']['target_1'],
                'target_2': best_signal['targets']['target_2'],
                'stop_loss': best_signal['targets']['stop_loss'],
                'risk_reward': best_signal['targets']['risk_reward'],
                'volatility': risk_metrics['volatility'],
                'max_drawdown': risk_metrics['max_drawdown']
            }

            print(f"  ✅ {symbol} için sinyal bulundu - Skor: {score}")
            return result

        return None


    def run_full_analysis(self):
        """Tüm BIST-30 hisseleri için analiz"""
        print("🚀 BIST Profesyonel Analiz Başlatılıyor...")
        print("=" * 60)

        symbols = self.fetcher.get_bist30_symbols()
        all_signals = []

        for symbol in symbols:
            time.sleep(random.uniform(1.5, 4.0)) # Her istek öncesi rastgele bekleme
            try:
                result = self.analyze_symbol(symbol)
                if result:
                    all_signals.append(result)
            except Exception as e:
                print(f"  ❌ {symbol} analizinde hata: {str(e)}")
                continue

        print("=" * 60)
        print(f"📊 Toplam {len(all_signals)} sinyal tespit edildi")

        # Sinyalleri filtrele
        filtered_signals = self.signal_filter.filter_signals(all_signals)
        print(f"🎯 Filtreden {len(filtered_signals)} sinyal geçti")

        # Rapor oluştur
        if filtered_signals:
            self.report_generator.generate_comprehensive_report(filtered_signals)

            # En iyi 5 sinyali göster
            print("\n🏆 EN İYİ 5 SİNYAL:")
            print("-" * 50)

            for i, signal in enumerate(filtered_signals[:5], 1):
                print(f"{i}. {signal['symbol']} - {signal['strategy']}")
                print(f"   Skor: {signal['score']}, R/R: {signal['risk_reward']:.1f}")
                print(f"   Hedef: {signal['target_1']:.2f} ₺, Stop: {signal['stop_loss']:.2f} ₺")
                print()
        else:
            print("⚠️ Filtrelenmiş sinyal bulunamadı")

        print("✅ Analiz tamamlandı!")
        return filtered_signals

# ----------------------------
# ANA ÇALIŞTIRMA FONKSIYONU
# ----------------------------
def main():
    """Ana çalıştırma fonksiyonu"""
    analyzer = BISTAnalyzer()

    try:
        # Tam analiz çalıştır
        signals = analyzer.run_full_analysis()

        if signals:
            print(f"\n📈 {len(signals)} adet yatırım fırsatı tespit edildi!")
            print("📄 Detaylı rapor HTML dosyasında oluşturuldu.")
        else:
            print("\n📉 Şu an için güçlü sinyal bulunamadı.")
            print("💡 Piyasa koşulları değiştiğinde tekrar çalıştırın.")

    except KeyboardInterrupt:
        print("\n⏹️ Kullanıcı tarafından durduruldu.")
    except Exception as e:
        print(f"\n❌ Beklenmeyen hata: {str(e)}")
        print("🔧 Veri bağlantısını kontrol edin ve tekrar deneyin.")

if __name__ == "__main__":
    main()