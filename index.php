<?php

declare(strict_types=1);

use App\Core\Middlewares\Swagger;
use App\Modules\Swagger\Controllers\SwaggerController;
use System\Container\Container;
use System\Starter\Starter;
use System\Router\Router;

// Requires
require_once __DIR__ . "/App/Config/Constants.php";
require_once __DIR__ . "/vendor/autoload.php";
foreach (glob(__DIR__ . "/System/Helpers/*.php") as $filename) {
   require_once $filename;
}

// Container
$container = new Container();
$container->register();

// Exception
set_exception_handler([$container->get('error'), 'handleException']);
set_error_handler([$container->get('error'), 'handleError']);

// Starter
$router = new Router($container);
$router->prefix('swagger')->middleware([Swagger::class])->group(function () use ($router) {
   $router->get('/', function () {
      require ROOT_DIR . '/Public/swagger/index.html';
   });

   $router->get('/swaggerJson', [SwaggerController::class, 'json']);

   $router->get('/list', function () {
      header('Content-type: application/json; charset=UTF-8');
      print(json_encode([
         [
            'url' => './swagger/swaggerJson',
            'name' => 'SwaggerController'
         ]
      ]));
   });
});

// error
// $route->error(function ($uri) {
//    header('HTTP/1.1 404 Not Found');
// });

$app = new Starter($router);
$app->run();