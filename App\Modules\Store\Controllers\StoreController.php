<?php

declare(strict_types=1);

namespace App\Modules\Store\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\Store\Services\StoreService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="Store", description="Mağaza işlemleri")
 */
class StoreController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private StoreService $service,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     tags={"Store"},
    *     path="/api/stores",
    *     summary="Tüm Mağazaları Listele",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Mağaza listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    * )
    */
   public function getAll() {
      $stores = $this->service->getAll();
      return $this->success($stores);
   }

   /**
    * @OA\Get(
    *     tags={"Store"},
    *     path="/api/stores/user",
    *     summary="Kullanıcıya Ait Mağazaları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Kullanıcıya ait mağazalar"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=404, description="Store not found"),
    *    )
    */
   public function getByOwner() {
      $userId = $this->auth->getUser()['id'];
      if (!$userId) {
         return $this->forbidden();
      }
      $stores = $this->service->getByOwner($userId);

      return $this->success($stores);
   }

   /**
    * @OA\Get(
    *     tags={"Store"},
    *     path="/api/stores/{user_id}",
    *     summary="Kullanıcıya Ait Mağazaları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="user_id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *         ),
    *     @OA\Response(response=200, description="Kullanıcıya ait mağazalar"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=404, description="Store not found"),
    *    )
    */

   public function getByUserId(int $userId) {
      $stores = $this->service->getByOwner($userId);
      return $this->success($stores);
   }

   /**
    * @OA\Post(
    *     tags={"Store"},
    *     path="/api/stores/create",
    *     summary="Yeni Mağaza Kaydı",
    *     operationId="addStore",
    *     security={{"Bearer": {}}},
    * @OA\Response(
    *    response=200,
    *    description="Mağaza oluşturuldu",
    *      @OA\JsonContent(
    *         @OA\Property(property="status", type="integer", example=200),
    *         @OA\Property(property="message", type="string", example="Success"),
    *         @OA\Property(
    *            property="data",
    *            @OA\Property(property="id", type="integer", example=123),
    *            @OA\Property(property="owner_id", type="integer", example=123),
    *            @OA\Property(property="name", type="string", example="StoreName")
    *         )
    *      )
    * ),
    * @OA\Response(response=400,description="Validasyon Hatası"),
    * @OA\Response(response=409,description="Çakışma Hatası"),
    * @OA\RequestBody(required=true,
    *    @OA\MediaType(mediaType="application/json",
    *    @OA\Schema(required={"name", "phone", "city", "country", "link","type", "owner_id"},
    *       @OA\Property(property="owner_id", type="integer", example=123),
    *       @OA\Property(property="name", type="string", example="StoreName"),
    *       @OA\Property(property="phone", type="string", example="1234567890"),
    *       @OA\Property(property="address", type="string", example="123 Main St"),
    *       @OA\Property(property="neighborhood", type="string", example="Neighborhood"),
    *       @OA\Property(property="district", type="string", example="District"),
    *       @OA\Property(property="city", type="string", example="City"),
    *       @OA\Property(property="country", type="string", example="Country"),
    *       @OA\Property(property="zip_code", type="string", example="12345"),
    *       @OA\Property(property="link", type="string", example="https://example.com"),
    *       @OA\Property(property="type", type="string", example="coiffeur"),
    *       @OA\Property(property="status", type="string", example="active")
    *    ))
    * ))
    */
   public function create() {
      $this->hasRole('admin');
      $data = $this->request->json();

      $result = $this->service->create($data);
      return $this->success([
         'id' => $result->getId(),
         'owner_id' => $result->getOwnerId(),
         'name' => $result->getName(),
         'phone' => $result->getPhone(),
         'link' => $result->getLink(),
      ]);
   }

   /**
    * @OA\Patch(
    *     path="/api/stores/update",
    *     tags={"Store"},
    *     summary="Mağazayı Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             @OA\Property(property="id", type="integer", example=1),
    *             @OA\Property(property="owner_id", type="integer", example=1),
    *             @OA\Property(property="name", type="string", example="John"),
    *             @OA\Property(property="phone", type="string", example="1234567890"),
    *             @OA\Property(property="address", type="string", example="123 Main St"),
    *             @OA\Property(property="neighborhood", type="string", example="Neighborhood"),
    *             @OA\Property(property="district", type="string", example="District"),
    *             @OA\Property(property="city", type="string", example="City"),
    *             @OA\Property(property="country", type="string", example="Country"),
    *             @OA\Property(property="zip_code", type="string", example="12345"),
    *             @OA\Property(property="link", type="string", example="https://example.com"),
    *             @OA\Property(property="type", type="string", example="coiffeur"),
    *             @OA\Property(property="status", type="string", example="active")
    *          )
    *     ),
    *     @OA\Response(response=200, description="Mağaza güncellendi"),
    *     @OA\Response(response=422, description="Validation error")
    * )
    */
   public function update() {
      $this->hasRole('admin');

      $userId = $this->auth->getUser()['id'];
      $data = $this->request->json();
      $this->service->update($userId, $data);
      return $this->success();
   }

   /**
    * @OA\Delete(
    *     tags={"Store"},
    *     path="/api/stores/delete/{id}",
    *     summary="Mağaza silinir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Mağaza silindi")
    * )
    */
   public function delete(int $storeId) {
      $this->hasRole('admin');

      $this->service->delete($storeId);
      return $this->success();
   }

   /**
    * @OA\Get(
    *     tags={"Store"},
    *     path="/api/stores/view/{id}",
    *     summary="Mağaza detaylarını getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(name="id", in="path", required=true, @OA\Schema(type="integer")),
    *     @OA\Response(response=200, description="Mağaza detayları")
    * )
    */
   public function getById(int $id) {
      $store = $this->service->getById($id);
      if (!$store) {
         return $this->error('Store not found');
      }
      return $this->success($store);
   }
}
