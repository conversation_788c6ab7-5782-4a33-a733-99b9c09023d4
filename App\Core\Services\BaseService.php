<?php declare(strict_types=1);

namespace App\Core\Services;

use App\Core\Repositories\IRepository;
use System\Exception\SystemException;

abstract class BaseService implements IService {
   /**
    * @param IRepository $repository
    */
   public function __construct(
      protected IRepository $repository
   ) {
   }

   /**
    * Tüm kayıtları getirir.
    *
    * @return array
    */
   public function getAll(): array {
      return $this->repository->getAll();
   }

   /**
    * ID ile kayıt getirir.
    *
    * @param int $id
    * @return object|null
    */
   public function getById(int $id): ?object {
      $item = $this->repository->getById($id);
      if (!$item) {
         throw new SystemException('Record not found');
      }
      return $item;
   }

   /**
    * Yeni kayıt oluşturur.
    *
    * @param array $data
    * @return object|null
    */
   public function create(array $data): ?object {
      return $this->repository->create($data);
   }

   /**
    * <PERSON><PERSON><PERSON> günceller.
    *
    * @param int $id
    * @param array $data
    * @return object|null
    */
   public function update(int $id, array $data): ?object {
      return $this->repository->update($id, $data);
   }

   /**
    * Kaydı siler.
    *
    * @param int $id
    * @return bool
    */
   public function delete(int $id): bool {
      return $this->repository->delete($id);
   }
}