<?php declare(strict_types=1);

namespace App\Modules\Employee\Services;

use App\Core\Services\BaseService;
use App\Modules\Employee\Repositories\EServiceRepository;

class EServiceService extends BaseService {
   public function __construct(
      EServiceRepository $repository
   ) {
      parent::__construct($repository);
   }

   public function getServicesByEmployeeId(int $employeeId) {
      return $this->repository->getById($employeeId);
   }
}