<?php

declare(strict_types=1);

namespace App\Modules\User\Repository;

use App\Core\Repositories\BaseRepository;
use App\Modules\User\Models\User;
use System\Database\Database;
use System\Exception\SystemException;
use System\Secure\Hash;

class UserRepository extends BaseRepository {
   private const MAX_ATTEMPTS = 5;
   private const LOCK_TIME = 1; //COMBAK -> süre ayarı yapılmalı
   private const MIN_PASSWORD_LENGTH = 8;
   private const DEFAULT_ROLE = 'customer';
   private const ALLOWED_GENDERS = ['male', 'female', 'other'];
   private array $roles = [
      'admin',
      'store_owner',
      'store_manager',
      'employee',
      'customer'
   ];
   protected string $table = 'users';

   public function __construct(
      protected Database $database,
      private Hash $hash
   ) {
   }

   /**
    * Kullanıcı oluşturur.
    *
    * @param array $registerData
    * @return User|null
    */
   public function create(array $registerData): ?User {

      $this->validateRequestRegisterData($registerData);

      $registerData['password'] = $this->hash->create($registerData['password']);
      $registerData['role'] = $registerData['role'] ?? self::DEFAULT_ROLE;

      $this->checkUniqueConstraints($registerData['email'], $registerData['phone']);

      $this->database
      ->table($this->table)
      ->insert(['name', 'middle_name', 'surname', 'sex', 'email', 'phone', 'password', 'role'])
      ->prepare()
      ->execute([
         'name' => $registerData['name'],
         'middle_name' => $registerData['middle_name'],
         'surname' => $registerData['surname'],
         'sex' => $registerData['sex'],
         'email' => $registerData['email'],
         'phone' => $registerData['phone'],
         'password' => $registerData['password'],
         'role' => $registerData['role']
      ]);

      return $this->getById((int)$this->database->lastInsertId());
   }

   /**
    * Kullanıcıyı günceller.
    *
    * @param int $userId
    * @param array $data
    * @return object|null
    */
   public function update(int $userId, array $data): ?object {
      $this->validateProfileData($data);

      try {
         $stmt = $this->database->prepare(
            'UPDATE users
                SET
                    name = :name,
                    middle_name = :middle_name,
                    surname = :surname,
                    phone = :phone,
                    email = :email,
                    sex = :sex
                WHERE id = :user_id
            '
         );

         $stmt->execute([
            'user_id' => $userId,
            'name' => $data['name'],
            'middle_name' => $data['middle_name'] ?? null,
            'surname' => $data['surname'],
            'phone' => $data['phone'],
            'email' => $data['email'],
            'sex' => $data['sex']
         ]);
         return $stmt->getAffectedRows() > 0 ? $this->getById($userId) : null;
      } catch (\PDOException) {
         throw new SystemException('Profile update failed');
      }
   }

   // delete fonksiyonu miras alınır.
   // getbyid miras alınır.

   /**
    * Başarısız giriş denemelerini artırır.
    *
    * @param null|string $email
    * @return void
    */
   public function incrementLoginAttempts(?string $email): void {
      if (!$email || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
         return;
      }
      // $this->database
      // ->table($this->table)
      // ->update(['login_attempts' => 'login_attempts + 1', 'last_attempt_at' => 'NOW()'])
      // ->where('email = :email')
      // ->execute(['email' => $email]);


      $this->database->prepare(
         'UPDATE users
          SET login_attempts = login_attempts + 1,
              last_attempt_at = NOW()
          WHERE email = :email
      '
      )->execute(['email' => $email]);
   }

   /**
    * Başarısız giriş denemelerini sıfırlar.
    *
    * @param null|string $email
    * @return void
    */
   public function resetLoginAttempts(?string $email): void {
      $this->database->prepare(
         'UPDATE users
          SET login_attempts = 0,
              last_attempt_at = NULL
          WHERE email = :email
      '
      )->execute(['email' => $email]);
      // $this->database
      // ->table($this->table)
      // ->debug()
      // ->update(['login_attempts' => 0, 'last_attempt_at' => 'NOW()'])
      // ->where('email = :email')
      // ->execute(['email' => $email]);
   }

   /**
    * Giriş istatistiklerini günceller.
    *
    * @param null|string $email
    * @return void
    */
   public function updateLoginStats(?string $email): void {
      $this->database
      ->table($this->table)
      ->update(['login_count' => ['login_count + 1'], 'last_login_at' => ['NOW()']])
      ->where('email = :email')
      ->prepare()
      ->execute(['email' => $email]);
   }

   /**
    * Hesabın kilitli olup olmadığını kontrol eder.
    *
    * @param User|null $user
    * @return void
    */
   public function checkAccountLock(?User $user): void { //COMBAK her denemede tekrar kilitleyerek sayacı baştan başlatıyor.
      if ($user->getLoginAttempts() >= self::MAX_ATTEMPTS) {
         $remainingTime = time() - strtotime($user->getLastAttemptAt());
         if ($remainingTime < self::LOCK_TIME) {
            throw new SystemException(
               'Hesap gecici olarak kilitlendi: ' . $remainingTime . ' saniye sonra tekrar deneyin.'
            );
         }
      }
   }

   /**
    * Profil verilerini doğrular.
    *
    * @param array $data
    * @throws SystemException
    */
   private function validateProfileData(array $data): void {
      if (empty($data['name']) || strlen($data['name']) > 50) {
         throw new SystemException('Invalid name (1-50 characters)');
      }

      if (!preg_match('/^[0-9]{10}$/', $data['phone'] ?? '')) {
         throw new SystemException('Phone must be 10 digits');
      }
   }

   /**
    * E-posta ve telefon numarasının kullanılıp kullanılmadığını kontrol eder.
    *
    * @param string $email
    * @param string $phone
    * @throws SystemException
    */
   private function checkUniqueConstraints(string $email, string $phone): void {
      if ($this->findByEmail($email)) {
         throw new SystemException('Email address already registered');
      }

      if ($this->findByPhone($phone)) {
         throw new SystemException('Phone number already registered');
      }
   }

   /**
    * Telefon numarasının kullanılıp kullanılmadığını kontrol eder.
    *
    * @param string $phone
    * @return object|null
    */
   public function findByPhone(string $phone): ?object {
      $stmt = $this->database->prepare(
         'SELECT * FROM users WHERE phone = :phone'
      );
      $stmt->execute(['phone' => $phone]);
      $user = $stmt->fetch();

      if (!$user) {
         return null;
      }

      return $this->mapToObject((array)$user);
   }

   /**
    * E-postanın kullanılıp kullanılmadığını kontrol eder.
    *
    * @param string $email
    * @return object|null
    */
   public function findByEmail(string $email): ?object {
      // $stmt = $this->database->prepare(
      //    'SELECT * FROM users WHERE email = :email'
      // );
      // $stmt->execute(['email' => $email]);
      // $user = $stmt->getRow();

      // if (!$user) {
      //    return null;
      // }
      $user = $this->database
      ->table($this->table)
      ->select(['*'])
      ->where('email = :email')
      ->prepare()
      ->execute(['email' => $email])
      ->fetch();

      var_dump($user);
      exit();

      return $this->mapToObject((array)$user);
   }

   /**
    * Kullanıcı oluşturma isteği için verileri doğrular.
    *
    * @param array $data
    * @return array
    * @throws SystemException
    */
   private function validateRequestRegisterData(array $data): mixed {
      $errors = [];

      $requiredFields = ['name', 'surname', 'sex', 'email', 'phone', 'password'];
      foreach ($requiredFields as $field) {
         if (empty($data[$field])) {
            $errors[] = "$field field is required";
         }
      }

      if (strlen($data['password'] ?? '') < self::MIN_PASSWORD_LENGTH) {
         $errors[] = 'Password must be at least ' . self::MIN_PASSWORD_LENGTH . ' characters';
      }

      if (!filter_var($data['email'] ?? '', FILTER_VALIDATE_EMAIL)) {
         $errors[] = 'Invalid email format';
      }

      if (!preg_match('/^[0-9]{10}$/', $data['phone'] ?? '')) {
         $errors[] = 'Phone must be 10 digits';
      }

      if (isset($data['sex']) && !in_array(strtolower($data['sex']), self::ALLOWED_GENDERS)) {
         $errors[] = 'Invalid gender selection';
      }

      return $errors;

      if ($errors !== []) {
         throw new SystemException('Validation Error', $errors[0]);
      }
   }


   // ROLES

   /**
    * Kullanılabilir rolleri getirir.
    *
    * @return array
    */
   public function getRoles(): array {
      return $this->roles;
   }

   /**
    * @param int $userId
    * @return object
    * @throws SystemException
    */
   public function getRoleById(int $userId): object {
      $stmt = $this->database->prepare(
         'SELECT
            role
         FROM users
         WHERE id = :user_id
         '
      );
      $stmt->execute([
         'user_id' => $userId,
      ]);
      return $stmt->fetch() ?: throw new SystemException('User not found');
   }

   /**
    * @param int $userId
    * @param string $role
    * @return bool
    * @throws SystemException
    */
   public function setRole(int $userId, string $role): bool {
      $this->validateRole($role);

      $stmt = $this->database->prepare(
         'UPDATE users
            SET role = :role
            WHERE id = :user_id
         '
      );
      $stmt->execute([
         'user_id' => $userId,
         'role' => $role
      ]);
      return $stmt->getAffectedRows() > 0;
   }

   /**
    * @param string $role
    * @throws SystemException
    */
   private function validateRole(string $role): void {
      if (!in_array($role, $this->roles)) {
         throw new SystemException('Invalid role');
      }
   }

   /**
    * @param int $userId
    * @param string $permissionAsking
    * @return bool
    */
   public function checkPermission(int $userId, string $permissionAsking): bool {
      $stmt = $this->database->prepare(
         'SELECT role
          FROM users
          WHERE id = :user_id
      '
      );
      $stmt->execute([
         'user_id' => $userId,
      ]);
      $result = $stmt->fetch();
      return $result && $result->role === $permissionAsking;
   }

   // PASSWORD

   private function updateUserPassword(int $userId, string $password): void {
      $hash = $this->hash->create($password);
      $this->database->prepare(
         'UPDATE users
          SET password = :hash
          WHERE id = :user_id
      '
      )->execute([
         'user_id' => $userId,
         'hash' => $hash
      ]);
   }
   public function requestReset(string $email): void {
      $user = $this->findByEmail($email);
      if (!$user) return; // E-posta varmış gibi davran

      // $token = $this->generateToken();
      // $this->saveToken($user->id, $token);
      // $this->sendResetEmail($email, $token);
      //TODO
   }

   public function resetPassword(string $token, string $newPassword) {
      //TODO
   }

   public function sendResetEmail(string $email, string $token): void {
      //TODO
   }
   public function getTableName(): string {
      return $this->table;
   }

   public function mapToObject(array $data): object {
      return new User(...(array)$data);
   }
}
