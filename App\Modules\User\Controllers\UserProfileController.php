<?php

declare(strict_types=1);

namespace App\Modules\User\Controllers;

use App\Core\Controllers\BaseController;
use App\Core\Middlewares\Auth;
use App\Core\Middlewares\CheckRole;
use App\Modules\User\Service\UserService;
use System\Http\Request;
use System\Http\Response;

/**
 * @OA\Tag(name="User Profile", description="User Profile işlemleri")
 */
class UserProfileController extends BaseController {
   public function __construct(
      Request $request,
      Response $response,
      Auth $auth,
      CheckRole $checkRole,
      private UserService $userService,
   ) {
      parent::__construct($request, $response, $auth, $checkRole);
   }

   /**
    * @OA\Get(
    *     path="/api/users",
    *     tags={"User Profile"},
    *     summary="Tüm Kullanıcıları Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200, description="Kullanıcı listesi"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *    )
    * )
    */
   public function getAll() {
      $this->hasRole('admin');
      $users = $this->userService->getAll();
      return $this->success($users);
   }

   /**
    * @OA\Delete(
    *     path="/api/users/delete/{id}",
    *     tags={"User Profile"},
    *     summary="Kullanıcıyı Sil",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Kullanıcı silindi")
    * )
    */

    public function delete(int $id) {
      $this->hasRole('admin');
      $this->userService->delete($id);
      return $this->success();
   }

   /**
    * @OA\Get(
    *     path="/api/users/{id}",
    *     tags={"User Profile"},
    *     summary="Kullanıcıyı Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Parameter(
    *         name="id",
    *         in="path",
    *         required=true,
    *         @OA\Schema(type="integer")
    *     ),
    *     @OA\Response(response=200, description="Kullanıcı getirildi")
    * )
    */

   public function getById(int $id) {
      $user = $this->userService->getById($id);
      return $this->success($user);
   }

   /**
    * @OA\Get(
    *     path="/api/profile",
    *     tags={"User Profile"},
    *     summary="Profil Bilgilerini Getir",
    *     security={{"Bearer": {}}},
    *     @OA\Response(response=200,description="Profil bilgileri"),
    *     @OA\Response(response=401, description="Unauthorized"),
    *     @OA\Response(response=404, description="User not found"),
    *    )
    * )
    */
   public function getProfile() {
      $userId = $this->auth->getUser()['id'];
      if (!$userId) {
         return $this->forbidden();
      }

      $profile = $this->userService->getById($userId);
      return $this->success($profile);
   }

   /**
    * @OA\Patch(
    *     path="/api/profile/update",
    *     tags={"User Profile"},
    *     summary="Profili Güncelle",
    *     security={{"Bearer": {}}},
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             @OA\Property(property="name", type="string", example="John"),
    *             @OA\Property(property="middle_name", type="string", example=""),
    *             @OA\Property(property="surname", type="string", example="Doe"),
    *             @OA\Property(property="phone", type="string", example="1234567890"),
    *             @OA\Property(property="email", type="string", example="<EMAIL>"),
    *             @OA\Property(property="sex", type="string", example="Male")
    *         )
    *     ),
    *     @OA\Response(response=200, description="Profil güncellendi"),
    *     @OA\Response(response=422, description="Validation error")
    * )
    */

   public function update() {
      $userId = $this->auth->getUser()['id'];
      if (!$userId) {
         return $this->forbidden();
      }

      $data = $this->request->json();
      $this->userService->update($userId, $data);
      return $this->success($data);
   }

   /**
    * @OA\Post(
    *     tags={"User Profile"},
    *     path="/api/register",
    *     summary="Yeni Kullanıcı Kaydı",
    *     operationId="registerUser",
    *     security={},
    * @OA\Response(
    *    response=201,
    *    description="Kayıt Başarılı",
    *      @OA\JsonContent(
    *         @OA\Property(property="status", type="integer", example=201),
    *         @OA\Property(property="message", type="string", example="Registration successful"),
    *         @OA\Property(
    *            property="data",
    *            @OA\Property(property="id", type="integer", example=123),
    *            @OA\Property(property="email", type="string", example="<EMAIL>")
    *         )
    *      )
    * ),
    * @OA\Response(response=400,description="Validasyon Hatası"),
    * @OA\Response(response=409,description="Çakışma Hatası"),
    * @OA\RequestBody(required=true,
    *    @OA\MediaType(mediaType="application/json",
    *    @OA\Schema(required={"name", "middle_name", "surname", "sex", "email", "phone", "password", "role"},
    *       @OA\Property(property="name", type="string", example="John"),
    *       @OA\Property(property="middle_name", type="string", example=""),
    *       @OA\Property(property="surname", type="string", example="Doe"),
    *       @OA\Property(property="sex", type="string", example="Male"),
    *       @OA\Property(property="email", type="string", example="<EMAIL>"),
    *       @OA\Property(property="phone", type="string", example="1234567890"),
    *       @OA\Property(property="password", type="string", example="12345678"),
    *       @OA\Property(property="role", type="string", example="admin")
    *    ))
    * ))
    */
   public function create() {
      $data = $this->request->json();
      $result = $this->userService->create($data);
      return $this->success($result);
   }


   /**
    * @OA\Post(
    *     path="/api/password/reset/request",
    *     tags={"User Profile"},
    *     summary="Şifre Sıfırlama Talebi Gönder",
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             @OA\Property(property="email", type="string", example="<EMAIL>")
    *         )
    *     ),
    *     @OA\Response(
    *         response=202,
    *         description="Talep alındı, e-posta gönderildi"
    *     )
    * )
    */
   public function requestReset() {
      $email = $this->request->json('email');
      $this->userService->requestReset($email);
      return $this->success();
   }


   /**
    * @OA\Post(
    *     path="/api/password/reset",
    *     tags={"User Profile"},
    *     summary="Şifreyi Sıfırla",
    *     @OA\RequestBody(
    *         required=true,
    *         @OA\JsonContent(
    *             @OA\Property(property="token", type="string", example="a1b2c3..."),
    *             @OA\Property(property="new_password", type="string", example="YeniSifre123!")
    *         )
    *     ),
    *     @OA\Response(
    *         response=200,
    *         description="Şifre başarıyla sıfırlandı"
    *     ),
    *     @OA\Response(
    *         response=400,
    *         description="Geçersiz token veya hatalı istek"
    *     )
    * )
    */
   public function resetPassword() {
      $data = $this->request->json();
      $success = $this->userService->resetPassword(
         $data['token'],
         $data['new_password']
      );

      if ($success) {
         return $this->success();
      }
      return $this->error('Geçersiz token');
   }
}
