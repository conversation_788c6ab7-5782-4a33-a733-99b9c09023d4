<?php

declare(strict_types=1);

namespace App\Modules\Agent\Services;

use App\Core\Validation\Validator; // Add Validator import
use App\Modules\Agent\Models\SalesAgent;
use App\Modules\Agent\Repositories\AgentRepository;
use System\Exception\SystemException;
use System\Http\Request;

class AgentService {
   // Define validation rules based on SalesAgent model
   private array $validationRules = [
      'user_id' => ['required', 'numeric'],
      'agent_code' => ['required', 'max:50'],
      'upper_agent_id' => ['numeric'],
      'commission_rate' => ['required', 'numeric'],
      'tier' => ['required', 'max:20'],
      'is_active' => ['numeric']
   ];

   public function __construct(
      private AgentRepository $repository,
      private Request $request,
      private Validator $validator // Inject Validator
   ) {
   }

   /**
    * Tüm satış temsilcilerini getirir.
    *
    * @return array
    */
   public function getAll(): array {
      $agents = $this->repository->getAll();
      return array_map(function ($agent) {
         return $agent->jsonSerialize();
      }, $agents);
   }

   /**
    * ID ile satış temsilcisini getirir.
    *
    * @param int $id
    * @return array
    */
   public function getById(int $id): array {
      $agent = $this->repository->getById($id);
      if (!$agent) {
         throw new SystemException('Agent not found');
      }
      return $agent->jsonSerialize();
   }

   /**
    * Kullanıcı ID'si ile satış temsilcisini getirir.
    *
    * @param int $userId
    * @return array|null
    */
   public function getByUserId(int $userId): ?array {
      $agent = $this->repository->getByUserId($userId);
      if (!$agent) {
         return null;
      }
      return $agent->jsonSerialize();
   }

   /**
    * Temsilci kodu ile satış temsilcisini getirir.
    *
    * @param string $agentCode
    * @return array|null
    */
   public function getByAgentCode(string $agentCode): ?array {
      $agent = $this->repository->getByAgentCode($agentCode);
      if (!$agent) {
         return null;
      }
      return $agent->jsonSerialize();
   }

   /**
    * Yeni satış temsilcisi oluşturur.
    *
    * @param array $data
    * @return SalesAgent
    */
   public function create(array $data): SalesAgent {
      // Validasyon işlemi
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validasyon hatası: ' . json_encode($this->validator->getErrors()));
      }

      // Benzersizlik kontrolü
      if (isset($data['agent_code']) && $this->repository->existsByAgentCode($data['agent_code'])) {
         throw new SystemException('Bu temsilci kodu zaten kullanılıyor');
      }

      return $this->repository->create($data);
   }

   /**
    * Satış temsilcisini günceller.
    *
    * @param int $id
    * @param array $data
    * @return SalesAgent
    */
   public function update(int $id, array $data): SalesAgent {
      // Temsilci varlığını kontrol et
      $agent = $this->repository->getById($id);
      if (!$agent) {
         throw new SystemException('Temsilci bulunamadı');
      }

      // Kullanıcı ID değişikliğini kontrol et
      if (isset($data['user_id']) && $agent->getUserId() !== (int)$data['user_id']) {
         throw new SystemException('Kullanıcı ID değiştirilemez');
      }

      // Sadece gönderilen alanlar için validasyon kurallarını uygula
      $updateRules = array_intersect_key($this->validationRules, $data);
      if (!$this->validator->validate($data, $updateRules)) {
         throw new SystemException('Validasyon hatası: ' . json_encode($this->validator->getErrors()));
      }

      // Temsilci kodu benzersizliğini kontrol et
      if (isset($data['agent_code']) && $data['agent_code'] !== $agent->getAgentCode() &&
          $this->repository->existsByAgentCode($data['agent_code'])) {
         throw new SystemException('Bu temsilci kodu zaten kullanılıyor');
      }

      return $this->repository->update($id, $data);
   }

   /**
    * Satış temsilcisini siler.
    *
    * @param int $id
    * @return bool
    */
   public function delete(int $id): bool {
      return $this->repository->delete($id);
   }
}
