<?php

declare(strict_types=1);

namespace App\Modules\Review\Repositories;

use App\Core\Repositories\BaseRepository;
use App\Modules\Review\Models\Review;
use System\Database\Database;
use System\Exception\SystemException;

class ReviewRepository extends BaseRepository {
   protected string $table = 'reviews';

   public function __construct(
      Database $database
   ) {
      parent::__construct($database);
   }

   /**
    * Veritabanı tablosunun adını döndürür.
    *
    * @return string
    */
   protected function getTableName(): string {
      return $this->table;
   }

   /**
    * Veritabanı sonucunu Review nesnesine dönüştürür.
    *
    * @param array $data
    * @return Review
    */
   /**
    * Veritabanı sonucunu Review nesnesine dönüştürür.
    *
    * @param array $data
    * @return Review
    */
   protected function mapToObject(array $data): Review {
      return new Review(...$data);
   }

   /**
    * Yeni bir yorum oluşturur.
    *
    * @param array $data
    * @return Review|null
    * @throws SystemException
    */
   public function create(array $data): ?Review {
      try {
         $stmt = $this->database->prepare(
            'INSERT INTO reviews (service_id, customer_id, rating, review, status)
             VALUES (:service_id, :customer_id, :rating, :review, :status)'
         );

         $stmt->execute([
            'service_id' => $data['service_id'],
            'customer_id' => $data['customer_id'],
            'rating' => $data['rating'],
            'review' => $data['review'] ?? null,
            'status' => $data['status'] ?? 'passive'
         ]);

         $id = $this->database->getLastId();
         return $this->getById((int)$id);
      } catch (\PDOException $e) {
         throw new SystemException('Yorum oluşturma işlemi başarısız oldu: ' . $e->getMessage());
      }
   }

   /**
    * Yorumu günceller.
    *
    * @param int $id
    * @param array $data
    * @return Review|null
    * @throws SystemException
    */
   public function update(int $id, array $data): ?Review {
      try {
         $setFields = [];
         $params = ['id' => $id];

         if (isset($data['service_id'])) {
            $setFields[] = 'service_id = :service_id';
            $params['service_id'] = $data['service_id'];
         }

         if (isset($data['customer_id'])) {
            $setFields[] = 'customer_id = :customer_id';
            $params['customer_id'] = $data['customer_id'];
         }

         if (isset($data['rating'])) {
            $setFields[] = 'rating = :rating';
            $params['rating'] = $data['rating'];
         }

         if (isset($data['review'])) {
            $setFields[] = 'review = :review';
            $params['review'] = $data['review'];
         }

         if (isset($data['response'])) {
            $setFields[] = 'response = :response';
            $params['response'] = $data['response'];
         }

         if (isset($data['status'])) {
            $setFields[] = 'status = :status';
            $params['status'] = $data['status'];
         }

         if (empty($setFields)) {
            return null;
         }

         $sql = 'UPDATE reviews SET ' . implode(', ', $setFields) . ' WHERE id = :id';
         $stmt = $this->database->prepare($sql);
         $stmt->execute($params);

         return $stmt->getAffectedRows() > 0 ? $this->getById($id) : null;
      } catch (\PDOException $e) {
         throw new SystemException('Yorum güncelleme işlemi başarısız oldu: ' . $e->getMessage());
      }
   }

   /**
    * Hizmet ID'sine göre yorumları getirir.
    *
    * @param int $serviceId
    * @return array<Review>
    */
   public function getByServiceId(int $serviceId): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT * FROM reviews WHERE service_id = :service_id'
         );
         $stmt->execute(['service_id' => $serviceId]);
         $reviews = $stmt->getAll();

         return array_map(function ($review) {
            return $this->mapToObject((array)$review);
         }, $reviews);
      } catch (\PDOException $e) {
         throw new SystemException('Hizmet IDsine göre yorumlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * Müşteri ID'sine göre yorumları getirir.
    *
    * @param int $customerId
    * @return array<Review>
    */
   public function getByCustomerId(int $customerId): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT * FROM reviews WHERE customer_id = :customer_id'
         );
         $stmt->execute(['customer_id' => $customerId]);
         $reviews = $stmt->getAll();

         return array_map(function ($review) {
            return $this->mapToObject((array)$review);
         }, $reviews);
      } catch (\PDOException $e) {
         throw new SystemException('Müşteri IDsine göre yorumlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }

   /**
    * Duruma göre yorumları getirir.
    *
    * @param string $status
    * @return array<Review>
    */
   public function getByStatus(string $status): array {
      try {
         $stmt = $this->database->prepare(
            'SELECT * FROM reviews WHERE status = :status'
         );
         $stmt->execute(['status' => $status]);
         $reviews = $stmt->getAll();

         return array_map(function ($review) {
            return $this->mapToObject((array)$review);
         }, $reviews);
      } catch (\PDOException $e) {
         throw new SystemException('Durum bilgisine göre yorumlar getirilirken hata oluştu: ' . $e->getMessage());
      }
   }
}
