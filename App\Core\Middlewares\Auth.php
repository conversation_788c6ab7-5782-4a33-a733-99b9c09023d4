<?php

declare(strict_types=1);

namespace App\Core\Middlewares;

use System\Http\Request;
use System\Http\Response;
use System\Jwt\Jwt;
use System\Session\Session;

class Auth {
   private $user = null;

   public function __construct(
      private Jwt $jwt,
      private Request $request,
      private Response $response,
      private Session $session
   ) {
   }

   /** Handle Auth Middleware */
   public function handle(callable $next): mixed {
      if (!preg_match('/Bearer\s(\S+)/', $this->request->authorization(), $matches) || empty($matches[1])) {
         // throw new JwtException('Token not found or invalid', 401);
         header('HTTP/1.1 401 Unauthorized');
         return null;
      }

      $token = $matches[1];
      $decoded = $this->jwt->decode($token);

      $this->user = [
         'id' => $decoded->sub,
         'role' => $decoded->role
      ];
      return $next();
   }

   /** Get User
    *
    * @return ?array
    */
   public function getUser(): ?array {
      return $this->user;
   }

   public function authenticate(): bool {
      return $this->handle(function() {
         return true;
      }) !== null;
   }

}
