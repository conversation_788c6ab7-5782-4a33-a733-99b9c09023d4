<?php

declare(strict_types=1);

namespace App\Modules\ServiceCategory\Services;

use App\Core\Validation\Validator; // Add Validator import
use App\Modules\ServiceCategory\Models\ServiceCategory;
use App\Modules\ServiceCategory\Repositories\ServiceCategoryRepository;
use System\Exception\SystemException;

class ServiceCategoryService {
   // Define validation rules based on ServiceCategory model
   private array $validationRules = [
      'name' => ['required', 'max:100'],
      'description' => ['max:255'] // Description might be optional
   ];

   public function __construct(
      private ServiceCategoryRepository $repository,
      private Validator $validator // Inject Validator
   ) {
   }
   public function getAll(): array {

      $categories = $this->repository->getAll();
      $categoryData = array_map(function ($category) {
         return [
            'id' => $category->getId(),
            'name' => $category->getName(),
            'description' => $category->getDescription(),
            'created_at' => $category->getCreatedAt(),
            'updated_at' => $category->getUpdatedAt()
         ];
      }, $categories);
      return $categoryData;
   }

   public function getById(int $id): array {
      $category = $this->repository->getById($id);
      if (!$category) {
         throw new SystemException('Category not found');
      }
      return $category->jsonSerialize();
   }

   public function create(array $data): ServiceCategory {
      // Add validation
      if (!$this->validator->validate($data, $this->validationRules)) {
         throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
      }

      // Check uniqueness *after* validation
      if ($this->repository->existsByName($data['name'])) {
         throw new SystemException('Category name already exists');
      }

      return $this->repository->create($data);
   }

   public function update(array $data): ?object {
       // Ensure 'id' is present in data for fetching the category
       if (!isset($data['id'])) {
           throw new SystemException('Category ID is required for update.');
       }
       $categoryId = (int)$data['id'];

       // Fetch the category first to ensure it exists
       $category = $this->repository->getById($categoryId);
       if (!$category) {
           throw new SystemException('Category not found');
       }

       // Validate provided data
       $updateRules = array_intersect_key($this->validationRules, $data);
       if (!$this->validator->validate($data, $updateRules)) {
           throw new SystemException('Validation failed: ' . json_encode($this->validator->getErrors()));
       }

       // Check uniqueness *after* validation, only if name is being updated
       if (isset($data['name'])) {
           $existingCategory = $this->repository->findByName($data['name']);
           if ($existingCategory && $existingCategory->getId() !== $categoryId) {
               throw new SystemException('Category name already exists');
           }
       }

       // Perform the update
       return $this->repository->update($categoryId, $data);
   }

   public function delete(int $id): bool {
      $category = $this->repository->getById($id);
      if ($category === null) {
         throw new SystemException('Category not found');
      }
      return $this->repository->delete($id);
   }

   // Remove the old private validateData method
   /*
   private function validateData(array $data): void {
      if (empty($data['name'])) {
         throw new ExceptionHandler('Name is required');
      }
      if ($this->repository->existsByName($data['name'])) {
         if (isset($data['id']) && $this->repository->getById((int)$data['id'])->getName() === $data['name']) {
            return;
         }
         throw new ExceptionHandler('Category name already exists');
      }
   }
   */
}
