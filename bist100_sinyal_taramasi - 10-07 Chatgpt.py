from tvDatafeed import TvDatafeed, Interval
import pandas_ta as ta
import pandas as pd

# BIST 100 sembolleri (<PERSON><PERSON><PERSON>, tam listeyi aşa<PERSON><PERSON><PERSON> e<PERSON>ec<PERSON>)
bist100_symbols = [
    "AKBNK", "ASELS", "BIMAS", "DOHOL", "EKGYO", "EREGL", "FROTO", "GARAN", "HEKTS", "ISCTR",
    "KCHOL", "KRDMD", "KOZAA", "KOZAL", "ODAS", "PETKM", "PGSUS", "SAHOL", "SISE", "SOKM",
    "TCELL", "THYA<PERSON>", "TKF<PERSON>", "TOASO", "TUPRS", "VESTL", "YKB<PERSON><PERSON>"
]

tv = TvDatafeed("karamizah", "206557ieCnN")

sinyal_gelenler = []

for symbol in bist100_symbols:
    try:
        df = tv.get_hist(symbol, exchange='BIST', interval=Interval.in_daily, n_bars=100)
        if df is None or df.empty:
            print(f"Veri alınamadı: {symbol}")
            continue

        # Teknik analiz indikatörlerini ekle
        df.ta.rsi(length=14, append=True)
        df.ta.macd(append=True)

        # Son satırdaki değerlerle sinyal kontrolü
        son = df.iloc[-1]
        if son[f'RSI_14'] < 30 and son['MACD_12_26_9'] > son['MACDs_12_26_9']:
            sinyal_gelenler.append(symbol)
            print(f"ALIM SİNYALİ: {symbol}")
    except Exception as e:
        print(f"{symbol} için hata: {e}")

# Sonuçları CSV olarak kaydet
if sinyal_gelenler:
    pd.DataFrame(sinyal_gelenler, columns=['Sembol']).to_csv('sinyal_gelenler.csv', index=False)
    print("\nSinyal gelen hisseler 'sinyal_gelenler.csv' dosyasına kaydedildi.")
else:
    print("\nBugün alım sinyali gelen hisse bulunamadı.")
